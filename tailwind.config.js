/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
      './app/**/*.{js,ts,jsx,tsx}',
      './components/**/*.{js,ts,jsx,tsx}',
    ],
    theme: {
      extend: {
        animation: {
          'slow-spin': 'slowSpin 20s linear infinite',
          'reverse-spin': 'reverseSpin 25s linear infinite',
          'line-draw': 'lineDraw 5s ease-in-out infinite',
          'line-draw-delayed': 'lineDraw 5s ease-in-out 1.5s infinite',
          'line-draw-delayed-more': 'lineDraw 5s ease-in-out 3s infinite',
        },
        keyframes: {
          slowSpin: {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' },
          },
          reverseSpin: {
            '0%': { transform: 'rotate(360deg)' },
            '100%': { transform: 'rotate(0deg)' },
          },
          lineDraw: {
            '0%': { strokeDashoffset: '1000', opacity: '0.1' },
            '40%': { strokeDashoffset: '0', opacity: '0.2' },
            '60%': { strokeDashoffset: '0', opacity: '0.2' },
            '100%': { strokeDashoffset: '-1000', opacity: '0.1' },
          },
        },
        inset: {
          '1/8': '12.5%',
        },
        backgroundImage: {
          'grid-pattern': "url(\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0V20M0 1H20' stroke='%23000000' stroke-opacity='0.2' stroke-width='0.5'/%3E%3C/svg%3E\")",
        },
      },
    },
    plugins: [],
  }