import { db } from './index';
import * as schema from './schema';

async function verifySchema() {
  try {
    console.log('Available tables in schema:');
    console.log('- profiles');
    console.log('- products');
    console.log('- orders');
    console.log('- orderItems');
    console.log('- discounts');
    console.log('- colors');
    console.log('- sizes');
    console.log('- inventory');
    console.log('- productVariants');
    
    const profileCount = await db.select().from(schema.profiles);
    console.log(`\nProfiles in database: ${profileCount.length}`);
    
    const productCount = await db.select().from(schema.products);
    console.log(`Products in database: ${productCount.length}`);
    
    const orderCount = await db.select().from(schema.orders);
    console.log(`Orders in database: ${orderCount.length}`);
    
    console.log('\nDrizzle ORM is successfully connected to your Supabase database!');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

verifySchema();