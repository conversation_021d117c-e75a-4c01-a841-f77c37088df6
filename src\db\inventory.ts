import { db } from './index';
import { inventory, products, productVariants, colors, sizes } from './schema';
import { eq, desc } from 'drizzle-orm';

export async function getProductInventory(productName: string) {
  try {
    console.log(`Fetching inventory for product: ${productName}`);

    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, productName))
      .limit(1);

    if (!product || product.length === 0) {
      console.log(`Product not found: ${productName}`);
      throw new Error('Product not found');
    }

    console.log(`Found product: ${product[0].id}`);

    // Get inventory data, but only for variants that have both color and size
    // This filters out legacy variants that don't have proper size information
    const inventoryData = await db
      .select({
        productId: inventory.productId,
        variantId: inventory.variantId,
        colorName: colors.name,
        colorValue: colors.value,
        sizeName: sizes.name,
        sizeValue: sizes.value,
        quantity: inventory.quantity,
        updatedAt: inventory.updatedAt,
      })
      .from(inventory)
      .innerJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .innerJoin(colors, eq(productVariants.colorId, colors.id))
      .innerJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id))
      .orderBy(desc(inventory.updatedAt));

    // Return inventory grouped by color and size with IDs
    const inventoryByColorSize: Record<string, Record<string, number>> = {};
    const variantInfo: Record<string, Record<string, { productId: string, variantId: string | null }>> = {};

    console.log(`Found ${inventoryData.length} inventory records`);

    inventoryData.forEach(item => {
      if (item.colorName && item.sizeName) {
        if (!inventoryByColorSize[item.colorName]) {
          inventoryByColorSize[item.colorName] = {};
          variantInfo[item.colorName] = {};
        }
        inventoryByColorSize[item.colorName][item.sizeName] = item.quantity || 0;
        variantInfo[item.colorName][item.sizeName] = {
          productId: item.productId,
          variantId: item.variantId
        };
        console.log(`Inventory: ${item.colorName} ${item.sizeName} = ${item.quantity} (updated: ${item.updatedAt})`);
      }
    });

    const result = {
      inventory: inventoryByColorSize,
      variantInfo,
      productId: product[0].id,
      lastUpdated: new Date().toISOString()
    };

    console.log('Returning inventory data:', result);
    return result;
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    throw error;
  }
}

export async function getAllInventory() {
  try {
    const inventoryData = await db
      .select({
        id: inventory.id,
        productId: products.id,
        productName: products.name,
        sku: products.sku,
        variantId: productVariants.id,
        variantSku: productVariants.sku,
        colorName: colors.name,
        colorValue: colors.value,
        sizeName: sizes.name,
        sizeValue: sizes.value,
        quantity: inventory.quantity,
        updatedAt: inventory.updatedAt,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .orderBy(desc(inventory.updatedAt));

    return inventoryData;
  } catch (error) {
    console.error('Error fetching all inventory:', error);
    throw error;
  }
}

export async function updateInventoryQuantity(inventoryId: string, quantity: number) {
  try {
    console.log(`Updating inventory ${inventoryId} to quantity ${quantity}`);

    const result = await db
      .update(inventory)
      .set({
        quantity: quantity,
        updatedAt: new Date().toISOString()
      })
      .where(eq(inventory.id, inventoryId))
      .returning();

    console.log('Inventory update result:', result);

    if (result.length === 0) {
      throw new Error('Inventory record not found');
    }

    return { success: true, updated: result[0] };
  } catch (error) {
    console.error('Error updating inventory:', error);
    throw error;
  }
}