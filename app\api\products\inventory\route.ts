import { NextRequest, NextResponse } from 'next/server';
import { getProductInventory } from '@/src/db/inventory';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productName = searchParams.get('productName');
    const refresh = searchParams.get('refresh');
    const timestamp = searchParams.get('t');

    console.log(`Inventory API called for ${productName} at ${timestamp} (refresh: ${refresh})`);

    if (!productName) {
      return NextResponse.json({ error: 'Product name is required' }, { status: 400 });
    }

    const data = await getProductInventory(productName);

    // Return with aggressive no-cache headers to ensure fresh data
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
        'Last-Modified': new Date().toUTCString(),
        'ETag': `"${Date.now()}"`,
        'Vary': '*'
      }
    });
  } catch (error) {
    console.error('Inventory API error:', error);
    return NextResponse.json({
      error: 'Failed to fetch inventory',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}