"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

export type CartItem = {
  id: string;
  name: string;
  price: string;
  quantity: number;
  color?: string;
  size?: string;
  imageSrc?: string;
  imageAlt?: string;
  variantId?: string;
};

type CartContextType = {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (itemId: string, color?: string, size?: string) => void;
  updateQuantity: (itemId: string, quantity: number, color?: string, size?: string) => void;
  clearCart: () => void;
  getCartTotal: () => number;
  getItemCount: () => number;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);

  // Load cart from localStorage when component mounts
  useEffect(() => {
    const savedCart = localStorage.getItem("cart");
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error("Failed to parse cart from localStorage:", error);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("cart", JSON.stringify(items));
  }, [items]);

  // Add item to cart
  const addItem = (item: CartItem) => {
    setItems(currentItems => {
      // Check if item already exists with same id, color, and size
      const existingItemIndex = currentItems.findIndex(
        i => i.id === item.id && 
             i.color === item.color && 
             i.size === item.size
      );

      if (existingItemIndex > -1) {
        // Update quantity of existing item
        const updatedItems = [...currentItems];
        updatedItems[existingItemIndex].quantity += item.quantity;
        return updatedItems;
      } else {
        // Add new item
        return [...currentItems, item];
      }
    });
  };

  // Remove item from cart
  const removeItem = (itemId: string, color?: string, size?: string) => {
    setItems(currentItems => 
      currentItems.filter(
        item => !(item.id === itemId && 
                 item.color === color && 
                 item.size === size)
      )
    );
  };

  // Update quantity of an item
  const updateQuantity = (itemId: string, quantity: number, color?: string, size?: string) => {
    if (quantity < 1) {
      removeItem(itemId, color, size);
      return;
    }

    setItems(currentItems => 
      currentItems.map(item => {
        if (item.id === itemId && 
            item.color === color && 
            item.size === size) {
          return { ...item, quantity };
        }
        return item;
      })
    );
  };

  // Clear the entire cart
  const clearCart = () => {
    setItems([]);
  };

  // Calculate total price of all items in cart
  const getCartTotal = () => {
    return items.reduce((total, item) => {
      const price = parseFloat(item.price.replace(/[^\d.]/g, ''));
      return total + (price * item.quantity);
    }, 0);
  };

  // Calculate total number of items in cart
  const getItemCount = () => {
    return items.reduce((count, item) => count + item.quantity, 0);
  };

  return (
    <CartContext.Provider 
      value={{ 
        items, 
        addItem, 
        removeItem, 
        updateQuantity, 
        clearCart, 
        getCartTotal, 
        getItemCount
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
} 