'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { SiteHeader } from '@/components/site-header'
import { SiteFooter } from '@/components/site-footer'

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname()
  
  const tabs = [
    { name: 'Account', href: '/settings' },
    { name: 'Password', href: '/settings/password' },
    { name: 'Address', href: '/settings/address' },
    { name: 'Orders', href: '/settings/orders' },
  ]

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      
      <div className="flex-1">
        <div className="container mx-auto px-4 py-12">
          <h1 className="text-3xl font-bold mb-8 text-white">Settings</h1>
          
          <div className="flex flex-col md:flex-row gap-8">
            {/* Side Navigation */}
            <aside className="w-full md:w-64 shrink-0">
              <nav className="space-y-1 bg-neutral-900 border border-neutral-800">
                {tabs.map((tab) => (
                  <Link
                    key={tab.href}
                    href={tab.href}
                    className={cn(
                      "block px-4 py-3 text-sm font-medium transition-colors border-l-2",
                      pathname === tab.href 
                        ? "bg-neutral-800 border-white text-white"
                        : "border-transparent text-gray-400 hover:text-white hover:bg-neutral-800"
                    )}
                  >
                    {tab.name}
                  </Link>
                ))}
              </nav>
            </aside>
            
            {/* Content */}
            <main className="flex-1 bg-neutral-900 border border-neutral-800 p-6">
              {children}
            </main>
          </div>
        </div>
      </div>
      
      <SiteFooter />
    </div>
  )
}