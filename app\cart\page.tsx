"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCart } from "@/context/cart-context";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";
import { CartItem } from "@/components/cart-item";

export default function CartPage() {
  // --- Hooks --- 
  const router = useRouter();
  const { items, removeItem, updateQuantity, getCartTotal } = useCart();
  const [mounted, setMounted] = useState(false);

  // Effect for hydration (Called Unconditionally)
  useEffect(() => {
    setMounted(true);
  }, []);
  // --- End of Hooks ---

  const handleContinueToCheckout = () => {
    router.push('/checkout');
  };

  // Conditional return for loading state
  if (!mounted) {
    return (
      <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
        <SiteHeader />
        <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold tracking-tight mb-8">Loading cart...</h1>
        </main>
        <SiteFooter />
      </div>
    );
  }

  // Logic/variables needed only when mounted
  const subtotal = getCartTotal();
  const estimatedShipping = 3.99;
  const estimatedTax = subtotal * 0.0775; // 7.75% tax
  const estimatedTotal = subtotal + estimatedShipping + estimatedTax;

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto w-full">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-4xl font-bold tracking-tight mb-3">Shopping Cart</h1>
          {items.length === 0 ? (
             <div className="mt-16 text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-16 h-16 mx-auto text-neutral-400"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-medium mb-4 text-white">Your cart is empty</h2>
              <p className="text-neutral-400 mb-8 max-w-md mx-auto">
                Looks like you haven&apos;t added anything to your cart yet.
                Explore our collection and find something you love.
              </p>
              <Link href="/#shop">
                <Button className="rounded-none px-8 py-6 bg-white text-neutral-900 hover:bg-gray-200">
                  CONTINUE SHOPPING
                </Button>
              </Link>
            </div>
          ) : (
            <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="border-b border-neutral-700 pb-3 mb-6 hidden md:grid grid-cols-12 text-sm text-neutral-400">
                  <div className="col-span-6">PRODUCT</div>
                  <div className="col-span-2 text-center">PRICE</div>
                  <div className="col-span-2 text-center">QUANTITY</div>
                  <div className="col-span-2 text-center">TOTAL</div>
                </div>
                
                <div className="space-y-6">
                  {items.map((item) => (
                    <CartItem 
                      key={`${item.id}-${item.color}-${item.size}`}
                      item={item}
                      onQuantityChange={updateQuantity}
                      onRemove={removeItem}
                    />
                  ))}
                </div>
                
                <div className="mt-8 flex justify-between items-center">
                  <Link href="/#shop">
                    <Button variant="outline" className="rounded-none border-white text-white hover:bg-neutral-800">
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="18" 
                        height="18" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="m15 18-6-6 6-6" />
                      </svg>
                      CONTINUE SHOPPING
                    </Button>
                  </Link>
                </div>
              </div>
              
              {/* Order Summary */}
              <div className="lg:col-span-1">
                <div className="bg-neutral-800 p-6 border border-neutral-700">
                  <h2 className="text-xl font-bold mb-6 text-white">Order Summary</h2>
                  
                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between">
                      <span className="text-neutral-400">Subtotal</span>
                      <span className="font-medium text-white">${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-neutral-400">Shipping</span>
                      <span className="text-neutral-300">${estimatedShipping.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-neutral-400">Tax (7.75%)</span>
                      <span className="text-neutral-300">${estimatedTax.toFixed(2)}</span>
                    </div>
                  </div>
                  
                  <div className="border-t border-neutral-700 pt-4 mb-6">
                    <div className="flex justify-between text-lg font-bold text-white">
                      <span>Estimated Total</span>
                      <span>${estimatedTotal.toFixed(2)}</span>
                    </div>
                  </div>

                  {/* Continue to Checkout Button */}
                  <Button 
                    onClick={handleContinueToCheckout}
                    className="w-full rounded-none py-6 bg-white text-neutral-900 hover:bg-gray-200 text-sm relative overflow-hidden group"
                    disabled={items.length === 0}
                  >
                    <span className="relative z-10 group-hover:text-neutral-900 transition-colors duration-300">CONTINUE TO CHECKOUT</span>
                    <span className="absolute inset-0 bg-white translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
                  </Button>
                  
                  <div className="mt-6 text-center text-sm text-neutral-400">
                    <p>We accept</p>
                    <div className="flex justify-center gap-2 mt-2">
                      <span className="bg-neutral-700 rounded px-2 py-1">Visa</span>
                      <span className="bg-neutral-700 rounded px-2 py-1">Mastercard</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      
      <SiteFooter />
    </div>
  );
} 