import React from 'react';
import Link from 'next/link';
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";

export default function ContactPage() {
  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />

      {/* Contact Section */}
      <section className="flex-1 py-16 md:py-24 relative overflow-hidden bg-neutral-900">
        <div className="container mx-auto px-4 md:px-8">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tighter mb-8 text-white">Contact Us</h1>
            
            <p className="text-xl md:text-2xl leading-relaxed mb-12 text-gray-200">
              We&apos;re here to help with any questions you might have about our products, orders, or anything else related to Revision.
            </p>

            <div className="space-y-10 mb-16">
              {/* Email Contact */}
              <div className="flex flex-col md:flex-row md:items-center gap-4 p-6 border border-neutral-800 bg-neutral-950/50 hover:border-neutral-700 transition-colors">
                <div className="md:w-16 flex items-center justify-center">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="24" 
                    height="24" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    className="text-gray-300"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-medium mb-2 text-white">Email</h2>
                  <p className="text-lg text-gray-300 mb-3">
                    For customer support, inquiries, and feedback.
                  </p>
                  <Link 
                    href="mailto:<EMAIL>" 
                    className="text-lg font-medium text-white hover:underline"
                  >
                    <EMAIL>
                  </Link>
                </div>
              </div>

              {/* Instagram Contact */}
              <div className="flex flex-col md:flex-row md:items-center gap-4 p-6 border border-neutral-800 bg-neutral-950/50 hover:border-neutral-700 transition-colors">
                <div className="md:w-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-gray-300"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-medium mb-2 text-white">Instagram</h2>
                  <p className="text-lg text-gray-300 mb-3">
                    Follow us for updates and send us a DM for quick responses.
                  </p>
                  <Link 
                    href="https://www.instagram.com/revision.ro/#" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="text-lg font-medium text-white hover:underline"
                  >
                    @revision.ro
                  </Link>
                </div>
              </div>
            </div>

            {/* Response Time Info */}
            <div className="p-6 border border-neutral-800 bg-neutral-950/50 mt-10">
              <h2 className="text-xl font-medium mb-4 text-white">Response Time</h2>
              <p className="text-base leading-relaxed text-gray-300 mb-4">
                We typically respond to all inquiries within 24-48 hours during business days.
              </p>
              <p className="text-base leading-relaxed text-gray-300">
                <span className="font-medium text-white">Business Hours:</span> Monday - Friday, 9:00 AM - 5:00 PM EST
              </p>
            </div>

            {/* Additional Note */}
            <div className="mt-12 text-center">
              <p className="text-lg text-gray-300">
                For the fastest response, reach out to us on Instagram <Link href="https://www.instagram.com/revision.ro/#" target="_blank" rel="noopener noreferrer" className="text-white hover:underline">@revision.ro</Link>
              </p>
            </div>
          </div>
        </div>
      </section>

      <SiteFooter />
    </div>
  );
}