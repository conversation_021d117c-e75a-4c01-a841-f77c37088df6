"use client";

import Image from "next/image";
import { CartItem as CartItemType } from "@/context/cart-context";

interface CartItemProps {
  item: CartItemType;
  onQuantityChange: (id: string, quantity: number, color?: string, size?: string) => void;
  onRemove: (id: string, color?: string, size?: string) => void;
}

export function CartItem({ item, onQuantityChange, onRemove }: CartItemProps) {
  const itemPrice = parseFloat(item.price.replace(/[^\d.]/g, ''));
  const itemTotal = itemPrice * item.quantity;
  
  return (
    <div className="border-b border-neutral-700 pb-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
        {/* Product Image & Info (Mobile layout stacks, desktop uses grid) */}
        <div className="col-span-6 flex gap-4">
          <div className="w-24 h-24 border border-neutral-700 relative">
            {item.imageSrc ? (
              <Image
                src={item.imageSrc}
                alt={item.imageAlt || item.name}
                fill
                sizes="96px"
                className="object-cover object-center"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-neutral-700">
                <span className="text-xs text-neutral-400">No Image</span>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="font-medium text-base mb-1 text-white">{item.name}</h3>
            {item.color && <p className="text-sm text-neutral-400 mb-1">Color: {item.color}</p>}
            {item.size && <p className="text-sm text-neutral-400">Size: {item.size}</p>}
            
            {/* Mobile only price */}
            <p className="text-sm mt-2 md:hidden">
              <span className="font-medium text-white">{item.price}</span>
            </p>
            
            <button 
              onClick={() => onRemove(item.id, item.color, item.size)}
              className="text-sm text-neutral-400 hover:text-white underline mt-3 md:mt-2"
            >
              Remove
            </button>
          </div>
        </div>
        
        {/* Price - Desktop */}
        <div className="col-span-2 text-center hidden md:block">
          <p className="font-medium text-white">{item.price}</p>
        </div>
        
        {/* Quantity */}
        <div className="col-span-2 flex justify-center">
          <div className="flex border border-neutral-600 h-10 w-28 bg-neutral-800">
            <button 
              className="w-8 flex items-center justify-center border-r border-neutral-600 hover:bg-neutral-700"
              onClick={() => onQuantityChange(item.id, Math.max(1, item.quantity - 1), item.color, item.size)}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <div className="flex-1 flex items-center justify-center font-medium text-white">
              {item.quantity}
            </div>
            <button 
              className="w-8 flex items-center justify-center border-l border-neutral-600 hover:bg-neutral-700"
              onClick={() => onQuantityChange(item.id, item.quantity + 1, item.color, item.size)}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </div>
        </div>
        
        {/* Total */}
        <div className="col-span-2 text-right md:text-center">
          <p className="font-medium text-white">${itemTotal.toFixed(2)}</p>
        </div>
      </div>
    </div>
  );
} 