import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  const supabase = await createClient()
  
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
  
  return NextResponse.json({ 
    session: session ? {
      user: {
        id: session.user.id,
        email: session.user.email,
        user_metadata: session.user.user_metadata
      },
      expires_at: session.expires_at
    } : null 
  })
}