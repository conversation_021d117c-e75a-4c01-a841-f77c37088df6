'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { OrderItem, Order } from '@/components/order-item'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/context/auth-context'

export default function OrdersPage() {
  const { user, isLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [orders, setOrders] = useState<Order[]>([])
  const [loadingTimeout, setLoadingTimeout] = useState(false)
  const router = useRouter()

  // Fallback timeout to prevent infinite loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading || loading) {
        setLoadingTimeout(true)
        setLoading(false)
      }
    }, 15000) // 15 second timeout

    return () => clearTimeout(timer)
  }, [isLoading, loading])

  useEffect(() => {
    const fetchOrders = async () => {
      if (!user) return

      try {
        const response = await fetch('/api/user/orders')
        if (response.ok) {
          const userOrders = await response.json()
          setOrders(userOrders)
        } else {
          setOrders([])
        }
      } catch {
        setOrders([])
      } finally {
        setLoading(false)
      }
    }

    if (!isLoading) {
      fetchOrders()
    }
  }, [user, isLoading])

  if ((isLoading || loading) && !loadingTimeout) {
    return (
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="mt-2">Loading orders...</p>
      </div>
    )
  }

  // Show timeout message if loading took too long
  if (loadingTimeout && !user) {
    return (
      <div className="text-center text-white">
        <p className="text-red-400">Loading timed out. Please refresh the page or try logging in again.</p>
      </div>
    )
  }

  if (!user) {
    return <div className="text-center text-white">Please log in to access your orders.</div>
  }
  
  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-xl font-medium mb-6 text-white">Order History</h2>
      
      {orders.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-400 mb-6">You haven&apos;t placed any orders yet.</p>
          <Button
            onClick={() => router.push('/collections')}
            className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4"
          >
            Browse Products
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {orders.map((order) => (
            <OrderItem key={order.id} order={order} />
          ))}
        </div>
      )}
    </div>
  )
}