import { pgTable, foreignKey, pgEnum, uuid, text, numeric, integer, unique, boolean, timestamp, varchar } from "drizzle-orm/pg-core"
  import { sql } from "drizzle-orm"

export const keyStatus = pgEnum("key_status", ['default', 'valid', 'invalid', 'expired'])
export const keyType = pgEnum("key_type", ['aead-ietf', 'aead-det', 'hmacsha512', 'hmacsha256', 'auth', 'shorthash', 'generichash', 'kdf', 'secretbox', 'secretstream', 'stream_xchacha20'])
export const aalLevel = pgEnum("aal_level", ['aal1', 'aal2', 'aal3'])
export const codeChallengeMethod = pgEnum("code_challenge_method", ['s256', 'plain'])
export const factorStatus = pgEnum("factor_status", ['unverified', 'verified'])
export const factorType = pgEnum("factor_type", ['totp', 'webauthn', 'phone'])
export const oneTimeTokenType = pgEnum("one_time_token_type", ['confirmation_token', 'reauthentication_token', 'recovery_token', 'email_change_token_new', 'email_change_token_current', 'phone_change_token'])
export const discountType = pgEnum("discount_type", ['percentage', 'fixed'])
export const orderStatus = pgEnum("order_status", ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
export const productStatus = pgEnum("product_status", ['draft', 'active', 'archived'])
export const action = pgEnum("action", ['INSERT', 'UPDATE', 'DELETE', 'TRUNCATE', 'ERROR'])
export const equalityOp = pgEnum("equality_op", ['eq', 'neq', 'lt', 'lte', 'gt', 'gte', 'in'])


export const orderItems = pgTable("order_items", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	orderId: uuid("order_id").notNull().references(() => orders.id, { onDelete: "cascade" } ),
	productId: uuid("product_id").notNull().references(() => products.id),
	variantId: uuid("variant_id").references(() => productVariants.id),
	name: text("name").notNull(),
	description: text("description"),
	price: numeric("price", { precision: 10, scale:  2 }).notNull(),
	quantity: integer("quantity").notNull(),
	subtotal: numeric("subtotal", { precision: 10, scale:  2 }).notNull(),
});

export const discounts = pgTable("discounts", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	code: text("code").notNull(),
	description: text("description"),
	type: discountType("type").notNull(),
	value: numeric("value", { precision: 10, scale:  2 }).notNull(),
	minOrderValue: numeric("min_order_value", { precision: 10, scale:  2 }),
	maxUses: integer("max_uses"),
	usedCount: integer("used_count").default(0),
	isActive: boolean("is_active").default(true),
	startsAt: timestamp("starts_at", { mode: 'string' }),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
},
(table) => {
	return {
		discountsCodeUnique: unique("discounts_code_unique").on(table.code),
	}
});

export const orders = pgTable("orders", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	email: text("email").notNull(),
	total: numeric("total", { precision: 10, scale:  2 }).notNull(),
	status: orderStatus("status").default('pending').notNull(),
	shippingAddress: text("shipping_address").notNull(),
	billingAddress: text("billing_address").notNull(),
	paymentIntentId: text("payment_intent_id"),
	discountTotal: numeric("discount_total", { precision: 10, scale:  2 }).default('0'),
	shippingTotal: numeric("shipping_total", { precision: 10, scale:  2 }).default('0'),
	subtotal: numeric("subtotal", { precision: 10, scale:  2 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const products = pgTable("products", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	name: text("name").notNull(),
	description: text("description"),
	price: text("price").notNull(),
	featuredImageUrl: text("featured_image_url"),
	status: productStatus("status").default('draft').notNull(),
	sku: text("sku"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const sizes = pgTable("sizes", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: text("name").notNull(),
	value: text("value").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const colors = pgTable("colors", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: text("name").notNull(),
	value: text("value").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const inventory = pgTable("inventory", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	productId: uuid("product_id").notNull().references(() => products.id, { onDelete: "cascade" } ),
	variantId: uuid("variant_id").references(() => productVariants.id, { onDelete: "cascade" } ),
	quantity: integer("quantity").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const productVariants = pgTable("product_variants", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	productId: uuid("product_id").notNull().references(() => products.id, { onDelete: "cascade" } ),
	sizeId: uuid("size_id").references(() => sizes.id),
	colorId: uuid("color_id").references(() => colors.id),
	price: text("price"),
	sku: text("sku"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const profiles = pgTable("profiles", {
	id: uuid("id").primaryKey().notNull(),
	firstName: varchar("first_name", { length: 50 }),
	lastName: varchar("last_name", { length: 50 }),
	addressLine1: varchar("address_line1", { length: 255 }),
	addressLine2: varchar("address_line2", { length: 255 }),
	city: varchar("city", { length: 100 }),
	state: varchar("state", { length: 100 }),
	postalCode: varchar("postal_code", { length: 20 }),
	country: varchar("country", { length: 2 }),
	isAdmin: boolean("is_admin").default(false).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
});