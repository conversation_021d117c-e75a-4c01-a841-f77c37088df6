"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { useCart } from "@/context/cart-context";
import { useAuth } from "@/context/auth-context";
import { useRouter } from "next/navigation";

export function SiteHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { getItemCount } = useCart();
  const [mounted, setMounted] = useState(false);
  const [itemCount, setItemCount] = useState(0);
  const { user, isAdmin, signOut } = useAuth();
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      setItemCount(getItemCount());
    }
  }, [mounted, getItemCount]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleSignOut = async () => {
    if (isLoggingOut) return;

    try {
      setIsLoggingOut(true);
      await signOut();

      // Close mobile menu if open
      setMobileMenuOpen(false);

      // Redirect to home page after logout
      router.push('/');
      router.refresh();
    } catch {
      // Even if there's an error, try to redirect
      router.push('/');
      router.refresh();
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <header className="border-b border-neutral-800">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-8">
        <div className="flex-1">
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-xl font-bold tracking-tighter text-white">REVISION</span>
          </Link>
        </div>
        
        <nav className="hidden md:flex items-center justify-center space-x-10 flex-1">
          <Link href="/#shop" className="text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
            SHOP
          </Link>
          <Link href="/collections" className="text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
            COLLECTIONS
          </Link>
          <Link href="/about" className="text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
            ABOUT
          </Link>
          <Link href="/contact" className="text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
            CONTACT
          </Link>
        </nav>
        
        <div className="flex items-center space-x-4 flex-1 justify-end">
          {mounted && (
            user ? (
              <>
                {isAdmin && (
                  <Link href="/admin/orders">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-sm tracking-wide text-gray-300 hover:text-white border border-neutral-800 hover:border-white hover:bg-transparent mr-2"
                    >
                      ADMIN
                    </Button>
                  </Link>
                )}
                <Link href="/settings">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-sm tracking-wide text-gray-300 hover:text-white border border-neutral-800 hover:border-white hover:bg-transparent mr-2"
                  >
                    SETTINGS
                  </Button>
                </Link>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                  disabled={isLoggingOut}
                  className="text-sm tracking-wide text-gray-300 hover:text-white border border-neutral-800 hover:border-white hover:bg-transparent disabled:opacity-50"
                >
                  {isLoggingOut ? 'LOGGING OUT...' : 'LOGOUT'}
                </Button>
              </>
            ) : (
              <Link href="/login">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="text-sm tracking-wide text-gray-300 hover:text-white border border-neutral-800 hover:border-white hover:bg-transparent"
                >
                  LOGIN
                </Button>
              </Link>
            )
          )}
          
          <Link href="/cart">
            <Button variant="ghost" size="icon" className="h-9 w-9 relative text-gray-300 hover:text-white hover:bg-neutral-800">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="20" 
                height="20" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className="h-5 w-5"
              >
                <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <path d="M16 10a4 4 0 0 1-8 0"></path>
              </svg>
              <span className="sr-only">Cart</span>
              {mounted && itemCount > 0 && (
                <span className="absolute -top-1.5 -right-1.5 flex h-5 w-5 items-center justify-center rounded-full bg-gray-700 text-[10px] font-medium text-white">
                  {itemCount}
                </span>
              )}
            </Button>
          </Link>

          <button
            onClick={toggleMobileMenu}
            className="md:hidden flex items-center justify-center h-9 w-9 text-gray-300 hover:text-white hover:bg-neutral-800 rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
            <span className="sr-only">Toggle menu</span>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-neutral-800 bg-neutral-900">
          <nav className="flex flex-col py-4 px-4">
            <Link href="/#shop" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
              SHOP
            </Link>
            <Link href="/collections" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
              COLLECTIONS
            </Link>
            <Link href="/about" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
              ABOUT
            </Link>
            <Link href="/contact" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors">
              CONTACT
            </Link>
            {mounted && user ? (
              <>
                {isAdmin && (
                  <Link href="/admin/orders" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors border-b border-neutral-800">
                    ADMIN
                  </Link>
                )}
                <Link href="/settings" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors border-b border-neutral-800">
                  SETTINGS
                </Link>
                <button
                  onClick={handleSignOut}
                  disabled={isLoggingOut}
                  className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors text-left border-b border-neutral-800 disabled:opacity-50"
                >
                  {isLoggingOut ? 'LOGGING OUT...' : 'LOGOUT'}
                </button>
              </>
            ) : (
              <Link href="/login" className="py-3 text-sm tracking-wide text-gray-300 hover:text-white transition-colors border-b border-neutral-800">
                LOGIN
              </Link>
            )}
          </nav>
        </div>
      )}
    </header>
  );
}
