import { db } from '../src/db/index';
import { inventory, products, productVariants, colors, sizes } from '../src/db/schema';
import { eq, and, isNull, isNotNull } from 'drizzle-orm';

async function fixMissingInventory() {
  try {
    console.log('🔧 Fixing missing inventory records for Cropped Zip Up Hoodie...\n');

    // 1. Find the product
    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    if (!product || product.length === 0) {
      console.log('❌ Product "Cropped Zip Up Hoodie" not found');
      return;
    }

    console.log('✅ Found product:', product[0].name);

    // 2. Find all variants that have both colorId and sizeId (proper variants)
    const properVariants = await db
      .select({
        variantId: productVariants.id,
        productId: productVariants.productId,
        colorId: productVariants.colorId,
        sizeId: productVariants.sizeId,
        colorName: colors.name,
        sizeName: sizes.name,
        sku: productVariants.sku
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(
        and(
          eq(productVariants.productId, product[0].id),
          isNotNull(productVariants.colorId),
          isNotNull(productVariants.sizeId)
        )
      );

    console.log(`\n📋 Found ${properVariants.length} proper variants (with color and size):`);
    properVariants.forEach(variant => {
      console.log(`  - ${variant.colorName} ${variant.sizeName} [${variant.variantId}]`);
    });

    // 3. Find variants without inventory records
    const variantsWithoutInventory = [];
    
    for (const variant of properVariants) {
      const existingInventory = await db
        .select()
        .from(inventory)
        .where(eq(inventory.variantId, variant.variantId))
        .limit(1);

      if (existingInventory.length === 0) {
        variantsWithoutInventory.push(variant);
      }
    }

    console.log(`\n🚨 Found ${variantsWithoutInventory.length} variants without inventory:`);
    variantsWithoutInventory.forEach(variant => {
      console.log(`  - ${variant.colorName} ${variant.sizeName} [${variant.variantId}]`);
    });

    // 4. Create missing inventory records
    if (variantsWithoutInventory.length > 0) {
      console.log('\n🔧 Creating missing inventory records...');
      
      for (const variant of variantsWithoutInventory) {
        try {
          const newInventory = await db.insert(inventory).values({
            productId: variant.productId,
            variantId: variant.variantId,
            quantity: 0 // Start with 0, admin can update later
          }).returning();

          console.log(`✅ Created inventory for ${variant.colorName} ${variant.sizeName}: ${newInventory[0].id}`);
        } catch (error) {
          console.error(`❌ Failed to create inventory for ${variant.colorName} ${variant.sizeName}:`, error);
        }
      }
    }

    // 5. Clean up legacy variants (those without proper size/color)
    console.log('\n🧹 Checking for legacy variants to clean up...');
    
    const legacyVariants = await db
      .select({
        variantId: productVariants.id,
        colorName: colors.name,
        sizeName: sizes.name,
        sku: productVariants.sku
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(
        and(
          eq(productVariants.productId, product[0].id),
          isNull(productVariants.sizeId)
        )
      );

    if (legacyVariants.length > 0) {
      console.log(`\n⚠️  Found ${legacyVariants.length} legacy variants (no size):`);
      legacyVariants.forEach(variant => {
        console.log(`  - ${variant.colorName} (no size) [${variant.variantId}]`);
      });
      console.log('Note: These variants have inventory but should not be displayed on the frontend.');
      console.log('The getProductInventory function should filter these out.');
    }

    // 6. Verify the fix
    console.log('\n🔍 Verifying the fix...');
    
    const allInventoryAfterFix = await db
      .select({
        inventoryId: inventory.id,
        variantId: inventory.variantId,
        quantity: inventory.quantity,
        colorName: colors.name,
        sizeName: sizes.name
      })
      .from(inventory)
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id));

    console.log('\n📦 All inventory records after fix:');
    allInventoryAfterFix.forEach(record => {
      if (record.colorName && record.sizeName) {
        console.log(`  ✅ ${record.colorName} ${record.sizeName}: ${record.quantity} units`);
      } else {
        console.log(`  🔸 Legacy: ${record.colorName || 'Unknown'} (no size): ${record.quantity} units`);
      }
    });

    console.log('\n🎉 Fix completed! All proper variants now have inventory records.');
    console.log('💡 Admin can now update quantities for all variants through the admin panel.');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

// Export for use as API endpoint
export { fixMissingInventory };

// Run directly if called as script
if (require.main === module) {
  fixMissingInventory().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Script error:', error);
    process.exit(1);
  });
}
