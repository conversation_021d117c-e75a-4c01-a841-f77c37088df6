import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  
  if (code) {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )
    
    const { data: { session }, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (error) {
      console.error('Error exchanging code for session:', error)
      return NextResponse.redirect(new URL('/login?error=auth_failed', requestUrl.origin))
    }
    
    console.log('Auth callback - session created for:', session?.user?.email)
    
    // If we have a new user with additional data, save their profile
    if (session?.user?.user_metadata) {
      const { 
        id, 
        user_metadata: {
          first_name,
          last_name,
          address_line1,
          address_line2,
          city,
          state,
          postal_code,
          country
        } = {}
      } = session.user
      
      // Only save profile if we have at least the required fields
      if (id && first_name && last_name && address_line1) {
        try {
          // Use Supabase directly to insert/update profile
          await supabase.from('profiles').upsert({
            id,
            first_name,
            last_name,
            address_line1,
            address_line2: address_line2 || null,
            city,
            state,
            postal_code,
            country: country || 'US',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
        } catch (error) {
          console.error('Error saving user profile:', error)
        }
      }
    }
  }

  // Redirect to home page
  // The middleware will handle session refresh
  return NextResponse.redirect(new URL('/', requestUrl.origin))
}