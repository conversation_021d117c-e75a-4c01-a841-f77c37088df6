import { db } from './index';
import { products } from './schema';

async function seedTestProduct() {
  try {
    console.log('Creating test product...');
    
    // Insert a test product with a specific UUID
    await db.insert(products).values({
      id: '00000000-0000-0000-0000-000000000001',
      userId: 'test-user',
      name: 'Test Product',
      description: 'This is a test product for development',
      price: '0.50',
      featuredImageUrl: '/assets/all-top.JPG',
      status: 'active',
      sku: 'TEST-001'
    }).onConflictDoNothing();
    
    console.log('Test product created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error creating test product:', error);
    process.exit(1);
  }
}

seedTestProduct();