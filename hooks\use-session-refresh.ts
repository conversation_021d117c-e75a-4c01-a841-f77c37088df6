"use client";

import { useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import { useRouter } from 'next/navigation';

/**
 * Hook to handle session refresh on page navigation and focus
 * This ensures authentication state stays in sync across the application
 */
export function useSessionRefresh() {
  const { refreshSession } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Refresh session when the page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshSession();
      }
    };

    // Refresh session when the page gains focus
    const handleFocus = () => {
      refreshSession();
    };

    // Refresh session on page load
    refreshSession();

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [refreshSession]);

  // Refresh session when router changes (navigation)
  useEffect(() => {
    const handleRouteChange = () => {
      // Small delay to ensure the route has changed
      setTimeout(() => {
        refreshSession();
      }, 100);
    };

    // Listen for route changes
    const originalPush = router.push;
    router.push = (...args) => {
      const result = originalPush.apply(router, args);
      handleRouteChange();
      return result;
    };

    return () => {
      router.push = originalPush;
    };
  }, [router, refreshSession]);
}
