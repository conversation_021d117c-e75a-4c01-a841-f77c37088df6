'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/context/auth-context'

interface AddressFormData {
  addressLine1: string
  addressLine2?: string
  city: string
  state: string
  postalCode: string
  country: string
}

export default function AddressSettings() {
  const { user, profile, isLoading, updateProfile } = useAuth()
  const [formData, setFormData] = useState<AddressFormData>({
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'US'
  })
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [saving, setSaving] = useState(false)
  const [loadingTimeout, setLoadingTimeout] = useState(false)

  // Fallback timeout to prevent infinite loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        setLoadingTimeout(true)
      }
    }, 15000) // 15 second timeout

    return () => clearTimeout(timer)
  }, [isLoading])

  // Update form fields when profile data is loaded
  useEffect(() => {
    if (profile) {
      setFormData({
        addressLine1: profile.addressLine1 || '',
        addressLine2: profile.addressLine2 || '',
        city: profile.city || '',
        state: profile.state || '',
        postalCode: profile.postalCode || '',
        country: profile.country || 'US'
      })
    }
  }, [profile])
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.addressLine1) {
      newErrors.addressLine1 = 'Address line 1 is required'
    }
    
    if (!formData.city) {
      newErrors.city = 'City is required'
    }
    
    if (!formData.state) {
      newErrors.state = 'State/Province is required'
    }
    
    if (!formData.postalCode) {
      newErrors.postalCode = 'Postal code is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleUpdateAddress = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)

    if (!validateForm() || !user) return

    try {
      setSaving(true)

      await updateProfile({
        addressLine1: formData.addressLine1,
        addressLine2: formData.addressLine2 || undefined,
        city: formData.city,
        state: formData.state,
        postalCode: formData.postalCode,
        country: formData.country,
      })

      setMessage({ type: 'success', text: 'Address updated successfully' })
    } catch (error: unknown) {
      setMessage({ type: 'error', text: error instanceof Error ? error.message : 'Error updating address' })
    } finally {
      setSaving(false)
    }
  }
  
  if (isLoading && !loadingTimeout) {
    return (
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="mt-2">Loading address...</p>
      </div>
    )
  }

  // Show timeout message if loading took too long
  if (loadingTimeout && !user) {
    return (
      <div className="text-center text-white">
        <p className="text-red-400">Loading timed out. Please refresh the page or try logging in again.</p>
      </div>
    )
  }

  if (!user) {
    return <div className="text-center text-white">Please log in to access settings.</div>
  }
  
  return (
    <div className="max-w-xl mx-auto">
      <h2 className="text-xl font-medium mb-6 text-white">Shipping Address</h2>
      
      {message && (
        <div 
          className={`p-3 mb-6 ${
            message.type === 'success' ? 'bg-green-900/30 border border-green-800 text-green-200' : 
                                         'bg-red-900/30 border border-red-800 text-red-200'
          }`}
        >
          {message.text}
        </div>
      )}
      
      <form onSubmit={handleUpdateAddress} className="space-y-6">
        <div>
          <Label htmlFor="addressLine1" className="text-white">Address Line 1</Label>
          <Input
            id="addressLine1"
            name="addressLine1"
            type="text"
            value={formData.addressLine1}
            onChange={handleInputChange}
            className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
              errors.addressLine1 ? 'border-red-500' : ''
            }`}
            placeholder="Street address"
          />
          {errors.addressLine1 && (
            <p className="mt-1 text-xs text-red-400">{errors.addressLine1}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="addressLine2" className="text-white">Address Line 2 (Optional)</Label>
          <Input
            id="addressLine2"
            name="addressLine2"
            type="text"
            value={formData.addressLine2 || ''}
            onChange={handleInputChange}
            className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
            placeholder="Apt, suite, unit, etc. (optional)"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="city" className="text-white">City</Label>
            <Input
              id="city"
              name="city"
              type="text"
              value={formData.city}
              onChange={handleInputChange}
              className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
                errors.city ? 'border-red-500' : ''
              }`}
              placeholder="City"
            />
            {errors.city && (
              <p className="mt-1 text-xs text-red-400">{errors.city}</p>
            )}
          </div>
          <div>
            <Label htmlFor="state" className="text-white">State / Province</Label>
            <Input
              id="state"
              name="state"
              type="text"
              value={formData.state}
              onChange={handleInputChange}
              className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
                errors.state ? 'border-red-500' : ''
              }`}
              placeholder="State"
            />
            {errors.state && (
              <p className="mt-1 text-xs text-red-400">{errors.state}</p>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="postalCode" className="text-white">Postal Code</Label>
            <Input
              id="postalCode"
              name="postalCode"
              type="text"
              value={formData.postalCode}
              onChange={handleInputChange}
              className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
                errors.postalCode ? 'border-red-500' : ''
              }`}
              placeholder="ZIP / Postal code"
            />
            {errors.postalCode && (
              <p className="mt-1 text-xs text-red-400">{errors.postalCode}</p>
            )}
          </div>
          <div>
            <Label htmlFor="country" className="text-white">Country</Label>
            <select
              id="country"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              className="w-full h-10 px-3 mt-1 bg-neutral-800 border border-neutral-700 text-white rounded-none"
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="MX">Mexico</option>
              <option value="GB">United Kingdom</option>
              <option value="AU">Australia</option>
              <option value="DE">Germany</option>
              <option value="FR">France</option>
              <option value="JP">Japan</option>
            </select>
          </div>
        </div>
        
        <Button
          type="submit"
          disabled={saving}
          className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4 w-full md:w-auto"
        >
          {saving ? 'Saving...' : 'Save Address'}
        </Button>
      </form>
    </div>
  )
}