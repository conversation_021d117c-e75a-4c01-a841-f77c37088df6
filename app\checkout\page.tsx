"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCart } from "@/context/cart-context";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import SquarePaymentForm from "@/components/square-payment-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { createClient } from "@/utils/supabase/client";

// Retrieve Square credentials from environment variables
const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;

// Form validation schema using Zod
const formSchema = z.object({
  fullName: z.string().min(2, { message: "Full name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().optional(),
  shippingAddress: z.object({
    street: z.string().min(1, { message: "Street address is required" }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    zip: z.string().min(1, { message: "ZIP code is required" }),
    country: z.string().min(1, { message: "Country is required" }),
  }),
  billingAddress: z.object({
    street: z.string().min(1, { message: "Street address is required" }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    zip: z.string().min(1, { message: "ZIP code is required" }),
    country: z.string().min(1, { message: "Country is required" }),
  }),
  useShippingAsBilling: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

export default function CheckoutPage() {
  const router = useRouter();
  const { items, getCartTotal, clearCart } = useCart();
  const [mounted, setMounted] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [useShippingForBilling, setUseShippingForBilling] = useState(true);
  const [isLoadingUserData, setIsLoadingUserData] = useState(true);
  
  // Create supabase client only once using useRef to avoid dependency issues
  const supabaseRef = React.useRef(createClient());

  // Initialize form with react-hook-form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      shippingAddress: {
        street: "",
        city: "",
        state: "",
        zip: "",
        country: "United States",
      },
      billingAddress: {
        street: "",
        city: "",
        state: "",
        zip: "",
        country: "United States",
      },
      useShippingAsBilling: true,
    },
  });

  // Effect for hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch user data for form auto-fill
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoadingUserData(true);
        // Get current user session using ref
        const supabase = supabaseRef.current;
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          // Fetch user's email
          const email = session.user.email;
          
          // Fetch profile from database
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();
          
          if (profile) {
            // Auto-fill form with user data
            form.setValue('fullName', `${profile.first_name} ${profile.last_name}`);
            form.setValue('email', email || '');
            form.setValue('phone', profile.phone || '');
            
            // Set shipping address
            form.setValue('shippingAddress.street', profile.address_line1 || '');
            form.setValue('shippingAddress.city', profile.city || '');
            form.setValue('shippingAddress.state', profile.state || '');
            form.setValue('shippingAddress.zip', profile.postal_code || '');
            form.setValue('shippingAddress.country', profile.country || 'United States');
            
            // Set billing address (same as shipping by default)
            if (useShippingForBilling) {
              form.setValue('billingAddress.street', profile.address_line1 || '');
              form.setValue('billingAddress.city', profile.city || '');
              form.setValue('billingAddress.state', profile.state || '');
              form.setValue('billingAddress.zip', profile.postal_code || '');
              form.setValue('billingAddress.country', profile.country || 'United States');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setIsLoadingUserData(false);
      }
    };

    if (mounted) {
      fetchUserData();
    }
  }, [mounted, form, useShippingForBilling]);

  // Redirect to cart if there are no items
  useEffect(() => {
    if (mounted && !isLoadingUserData && items.length === 0) {
      router.push("/cart");
    }
  }, [mounted, items, router, isLoadingUserData]);

  const handleShippingAsBillingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setUseShippingForBilling(isChecked);
    
    // If checked, copy shipping address to billing address
    if (isChecked) {
      const shippingValues = form.getValues('shippingAddress');
      form.setValue('billingAddress.street', shippingValues.street);
      form.setValue('billingAddress.city', shippingValues.city);
      form.setValue('billingAddress.state', shippingValues.state);
      form.setValue('billingAddress.zip', shippingValues.zip);
      form.setValue('billingAddress.country', shippingValues.country);
    }
  };

  const handlePaymentSuccess = (paymentDetails: Record<string, unknown>) => {
    setPaymentError(null);
    
    // Navigate to order confirmation with order ID
    const orderId = paymentDetails.orderId as string;
    clearCart();
    router.push(`/order-confirmation?orderId=${orderId}`);
  };

  const handlePaymentFailure = (error: unknown) => {
    let message = "An unexpected error occurred during payment.";
    if (error instanceof Error) {
      message = error.message;
    }
    setPaymentError(message);
  };


  // Calculate order summary values
  const subtotal = getCartTotal();
  const shipping: number = 3.99;
  const tax: number = subtotal * 0.0775; // 7.75% tax
  const total = subtotal + shipping + tax;

  // Check if Square is properly configured
  const isSquareReady = squareApplicationId && squareLocationId;

  if (!mounted || isLoadingUserData) {
    return (
      <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
        <SiteHeader />
        <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold tracking-tight mb-8">Loading checkout...</h1>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        </main>
        <SiteFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto w-full">
        <div className="max-w-6xl mx-auto">
          {/* Checkout Progress */}
          <div className="mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white text-neutral-900 rounded-full flex items-center justify-center font-semibold text-sm">1</div>
                <div className="text-white font-medium ml-2">Cart</div>
              </div>
              <div className="w-16 sm:w-24 h-[1px] mx-2 bg-neutral-700"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white text-neutral-900 rounded-full flex items-center justify-center font-semibold text-sm">2</div>
                <div className="text-white font-medium ml-2">Checkout</div>
              </div>
            </div>
          </div>

          <h1 className="text-4xl font-bold tracking-tight mb-8">Checkout</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Customer Information Form */}
            <div className="lg:col-span-2 space-y-8">
              <Form {...form}>
                <div className="space-y-8">
                  <div className="bg-neutral-800 p-6 border border-neutral-700">
                    <h2 className="text-xl font-bold mb-6 text-white">Customer Information</h2>
                    
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="fullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Full Name *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="John Doe" 
                                className="bg-neutral-900 border-neutral-700 text-white" 
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Email Address *</FormLabel>
                            <FormControl>
                              <Input 
                                type="email" 
                                placeholder="<EMAIL>" 
                                className="bg-neutral-900 border-neutral-700 text-white" 
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Phone Number (Optional)</FormLabel>
                            <FormControl>
                              <Input 
                                type="tel" 
                                placeholder="(*************" 
                                className="bg-neutral-900 border-neutral-700 text-white" 
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <div className="bg-neutral-800 p-6 border border-neutral-700">
                    <h2 className="text-xl font-bold mb-6 text-white">Shipping Address</h2>
                    
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="shippingAddress.street"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Street Address *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="123 Main St" 
                                className="bg-neutral-900 border-neutral-700 text-white" 
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="shippingAddress.city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">City *</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="New York" 
                                  className="bg-neutral-900 border-neutral-700 text-white" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="shippingAddress.state"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">State *</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="NY" 
                                  className="bg-neutral-900 border-neutral-700 text-white" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="shippingAddress.zip"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">ZIP Code *</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="10001" 
                                  className="bg-neutral-900 border-neutral-700 text-white" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="shippingAddress.country"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">Country *</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="United States" 
                                  className="bg-neutral-900 border-neutral-700 text-white" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="flex items-center mt-4">
                        <input
                          type="checkbox"
                          id="useShippingAsBilling"
                          checked={useShippingForBilling}
                          onChange={handleShippingAsBillingChange}
                          className="mr-2"
                        />
                        <label htmlFor="useShippingAsBilling" className="text-white">
                          Use shipping address as billing address
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  {!useShippingForBilling && (
                    <div className="bg-neutral-800 p-6 border border-neutral-700">
                      <h2 className="text-xl font-bold mb-6 text-white">Billing Address</h2>
                      
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="billingAddress.street"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">Street Address *</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="123 Main St" 
                                  className="bg-neutral-900 border-neutral-700 text-white" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="billingAddress.city"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">City *</FormLabel>
                                <FormControl>
                                  <Input 
                                    placeholder="New York" 
                                    className="bg-neutral-900 border-neutral-700 text-white" 
                                    {...field} 
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="billingAddress.state"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">State *</FormLabel>
                                <FormControl>
                                  <Input 
                                    placeholder="NY" 
                                    className="bg-neutral-900 border-neutral-700 text-white" 
                                    {...field} 
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="billingAddress.zip"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">ZIP Code *</FormLabel>
                                <FormControl>
                                  <Input 
                                    placeholder="10001" 
                                    className="bg-neutral-900 border-neutral-700 text-white" 
                                    {...field} 
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="billingAddress.country"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">Country *</FormLabel>
                                <FormControl>
                                  <Input 
                                    placeholder="United States" 
                                    className="bg-neutral-900 border-neutral-700 text-white" 
                                    {...field} 
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="bg-neutral-800 p-6 border border-neutral-700">
                    <h2 className="text-xl font-bold mb-6 text-white">Payment Information</h2>
                    
                    {paymentError && (
                      <div className="mb-4 p-3 bg-red-900 border border-red-700 text-red-200 rounded">
                        <p className="font-bold">Payment Error</p>
                        <p className="text-sm">{paymentError}</p>
                      </div>
                    )}
                    
                    {isSquareReady ? (
                      <div className="square-payment-wrapper">
                        <SquarePaymentForm
                          applicationId={squareApplicationId!}
                          locationId={squareLocationId!}
                          amount={total}
                          orderData={{
                            email: form.getValues('email'),
                            subtotal: subtotal,
                            shipping: shipping,
                            tax: tax,
                            total: total,
                            shippingAddress: form.getValues('shippingAddress'),
                            billingAddress: form.getValues('billingAddress'),
                            items: items.map(item => ({
                              productId: item.id,
                              name: item.name,
                              description: `${item.color || ''} ${item.size || ''}`.trim(),
                              price: parseFloat(item.price.replace(/[^\d.]/g, '')),
                              quantity: item.quantity,
                              variantId: item.variantId
                            }))
                          }}
                          onPaymentSuccess={handlePaymentSuccess}
                          onPaymentFailure={handlePaymentFailure}
                        />
                      </div>
                    ) : (
                      <div className="text-center text-neutral-400 text-sm">
                        Payment system is currently unavailable. Please check configuration.
                      </div>
                    )}
                  </div>

                </div>
              </Form>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-neutral-800 p-6 border border-neutral-700 sticky top-4">
                <h2 className="text-xl font-bold mb-6 text-white">Order Summary</h2>
                
                <div className="space-y-4 mb-6">
                  {items.map((item) => (
                    <div key={`${item.id}-${item.color}-${item.size}`} className="flex items-center gap-3 py-3 border-b border-neutral-700">
                      {item.imageSrc && (
                        <div className="w-16 h-16 bg-neutral-700 shrink-0 relative">
                          <Image 
                            src={item.imageSrc} 
                            alt={item.imageAlt || item.name} 
                            className="object-cover" 
                            fill
                            sizes="64px"
                          />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-white truncate">{item.name}</p>
                        {item.color && item.size && (
                          <p className="text-sm text-neutral-400">
                            {item.color} / {item.size}
                          </p>
                        )}
                        <div className="flex justify-between items-center mt-1">
                          <p className="text-sm text-neutral-400">Qty: {item.quantity}</p>
                          <p className="font-medium text-white">${(parseFloat(item.price.replace(/[^\d.]/g, '')) * item.quantity).toFixed(2)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Subtotal</span>
                    <span className="text-white">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Shipping</span>
                    <span className="text-white">{shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Tax</span>
                    <span className="text-white">{tax === 0 ? 'FREE' : `$${tax.toFixed(2)}`}</span>
                  </div>
                </div>
                
                <div className="border-t border-neutral-700 pt-4 mb-6">
                  <div className="flex justify-between text-lg font-bold text-white">
                    <span>Total</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>
                
                <div className="mt-6">
                  <Link href="/cart">
                    <Button variant="outline" className="w-full rounded-none border-white text-white hover:bg-neutral-800 mb-4">
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="18" 
                        height="18" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="m15 18-6-6 6-6" />
                      </svg>
                      RETURN TO CART
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <SiteFooter />
    </div>
  );
}