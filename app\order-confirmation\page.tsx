"use client";

import { useEffect, useState, Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";
import type { TransformedOrder, OrderItem } from "@/src/types/order";

function OrderConfirmationContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [orderData, setOrderData] = useState<TransformedOrder | null>(null);
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    setMounted(true);
    
    const fetchOrder = async () => {
      const orderId = searchParams.get('orderId');
      
      if (!orderId) {
        router.push('/');
        return;
      }
      
      try {
        const response = await fetch(`/api/orders/${orderId}`);
        
        if (!response.ok) {
          router.push('/');
          return;
        }
        
        const order = await response.json();
        
        if (!order) {
          router.push('/');
          return;
        }
        
        // Transform order data to match the expected format
        const transformedOrder = {
          orderId: order.id,
          orderDate: order.createdAt,
          items: order.items.map((item: OrderItem) => ({
            id: item.productId,
            name: item.name,
            price: `$${parseFloat(item.price).toFixed(2)}`,
            quantity: item.quantity,
            description: item.description,
            total: parseFloat(item.subtotal),
            color: item.color,
            size: item.size,
            imageSrc: item.imageSrc,
            imageAlt: item.imageAlt
          })),
          subtotal: parseFloat(order.subtotal),
          shipping: parseFloat(order.shippingTotal),
          tax: parseFloat(order.total) - parseFloat(order.subtotal) - parseFloat(order.shippingTotal),
          total: parseFloat(order.total),
          payment: {
            status: order.status,
            paymentIntentId: order.paymentIntentId
          },
          customer: {
            email: order.email
          },
          shippingAddress: order.shippingAddress,
          billingAddress: order.billingAddress
        };
        
        setOrderData(transformedOrder);
      } catch (error) {
        console.error('Error fetching order:', error);
        router.push('/');
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrder();
  }, [router, searchParams]);
  
  if (!mounted || loading || !orderData) {
    return (
      <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
        <SiteHeader />
        <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        </main>
        <SiteFooter />
      </div>
    );
  }
  
  const orderDate = new Date(orderData.orderDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto w-full">
        <div className="max-w-3xl mx-auto text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-600 mb-6">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="28" 
              height="28" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="text-white"
            >
              <path d="M20 6 9 17l-5-5" />
            </svg>
          </div>
          
          <h1 className="text-4xl font-bold tracking-tight mb-4">Order Confirmed!</h1>
          <p className="text-xl text-neutral-300 mb-2">Thank you for your purchase!</p>
          <p className="text-neutral-400 mb-8">A confirmation email has been sent to {orderData.customer.email}</p>
          
          <div className="bg-neutral-800 p-8 border border-neutral-700 mb-8 text-left">
            <div className="border-b border-neutral-700 pb-6 mb-6">
              <h2 className="text-2xl font-semibold mb-4">Order Information</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-neutral-400">Order Number</p>
                  <p className="font-medium">#{orderData.orderId}</p>
                </div>
                <div>
                  <p className="text-neutral-400">Order Date</p>
                  <p className="font-medium">{orderDate}</p>
                </div>
                <div>
                  <p className="text-neutral-400">Payment Status</p>
                  <p className="font-medium capitalize">{orderData.payment.status}</p>
                </div>
                <div>
                  <p className="text-neutral-400">Total Amount</p>
                  <p className="font-medium">${orderData.total.toFixed(2)}</p>
                </div>
              </div>
            </div>
            
            <div className="border-b border-neutral-700 pb-6 mb-6">
              <h3 className="text-lg font-medium mb-4">Items Ordered</h3>
              <div className="space-y-4">
                {orderData.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4">
                    {item.imageSrc && (
                      <div className="w-20 h-20 bg-neutral-700 shrink-0 relative">
                        <Image 
                          src={item.imageSrc} 
                          alt={item.imageAlt || item.name} 
                          className="object-cover" 
                          fill
                          sizes="80px"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <p className="font-medium">{item.name}</p>
                      {item.color && item.size && (
                        <p className="text-sm text-neutral-400">
                          {item.color} / {item.size}
                        </p>
                      )}
                      <p className="text-sm text-neutral-400">Qty: {item.quantity}</p>
                    </div>
                    <div>
                      <p className="font-medium">${item.total.toFixed(2)}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 space-y-2">
                <div className="flex justify-between">
                  <span className="text-neutral-400">Subtotal</span>
                  <span>${orderData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-400">Shipping</span>
                  <span>${orderData.shipping.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-400">Tax</span>
                  <span>${orderData.tax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg pt-2 border-t border-neutral-700">
                  <span>Total</span>
                  <span>${orderData.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Shipping Address</h3>
                <div className="text-neutral-400">
                  <p>{orderData.shippingAddress.street}</p>
                  <p>{orderData.shippingAddress.city}, {orderData.shippingAddress.state} {orderData.shippingAddress.zip}</p>
                  <p>{orderData.shippingAddress.country}</p>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Billing Address</h3>
                <div className="text-neutral-400">
                  <p>{orderData.billingAddress.street}</p>
                  <p>{orderData.billingAddress.city}, {orderData.billingAddress.state} {orderData.billingAddress.zip}</p>
                  <p>{orderData.billingAddress.country}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/#shop">
              <Button className="rounded-none px-8 py-6 bg-white text-neutral-900 hover:bg-gray-200 w-full sm:w-auto">
                CONTINUE SHOPPING
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button variant="outline" className="rounded-none px-8 py-6 border-white text-white hover:bg-neutral-800 w-full sm:w-auto">
                VIEW ORDER HISTORY
              </Button>
            </Link>
          </div>
        </div>
      </main>
      <SiteFooter />
    </div>
  );
}

export default function OrderConfirmationPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
        <SiteHeader />
        <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        </main>
        <SiteFooter />
      </div>
    }>
      <OrderConfirmationContent />
    </Suspense>
  );
}