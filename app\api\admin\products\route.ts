import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/src/db/auth';
import { db } from '@/src/db/index';
import { products, colors, sizes, productVariants, inventory } from '@/src/db/schema';
import { eq } from 'drizzle-orm';

interface ProductRequestData {
  name: string;
  description?: string;
  price: string;
  sku: string;
  featuredImageUrl?: string;
  colors: { name: string; value: string }[];
  sizes: { name: string; value: string }[];
  initialStock: number;
}

export async function POST(request: NextRequest) {
  try {
    // Require admin access
    const user = await requireAdmin();

    const data: ProductRequestData = await request.json();

    // Validate required fields
    if (!data.name || !data.price || !data.sku) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (!data.colors || data.colors.length === 0 || !data.sizes || data.sizes.length === 0) {
      return NextResponse.json({ error: 'At least one color and size must be selected' }, { status: 400 });
    }

    // Start a transaction to create product, variants, and inventory
    const result = await db.transaction(async (tx) => {
      // Create the product
      const [newProduct] = await tx.insert(products).values({
        userId: user.id,
        name: data.name,
        description: data.description || null,
        price: data.price,
        sku: data.sku,
        featuredImageUrl: data.featuredImageUrl || null,
        status: 'active',
      }).returning();

      // Get or create colors
      const colorIds: { [key: string]: string } = {};
      for (const colorData of data.colors) {
        const [existingColor] = await tx
          .select()
          .from(colors)
          .where(eq(colors.name, colorData.name))
          .limit(1);

        if (existingColor) {
          colorIds[colorData.name] = existingColor.id;
        } else {
          const [newColor] = await tx.insert(colors).values({
            name: colorData.name,
            value: colorData.value,
          }).returning();
          colorIds[colorData.name] = newColor.id;
        }
      }

      // Get or create sizes
      const sizeIds: { [key: string]: string } = {};
      for (const sizeData of data.sizes) {
        const [existingSize] = await tx
          .select()
          .from(sizes)
          .where(eq(sizes.name, sizeData.name))
          .limit(1);

        if (existingSize) {
          sizeIds[sizeData.name] = existingSize.id;
        } else {
          const [newSize] = await tx.insert(sizes).values({
            name: sizeData.name,
            value: sizeData.value,
          }).returning();
          sizeIds[sizeData.name] = newSize.id;
        }
      }

      // Create variants and inventory for each color/size combination
      let createdVariants = 0;

      for (const colorData of data.colors) {
        for (const sizeData of data.sizes) {
          // Create variant
          const [variant] = await tx.insert(productVariants).values({
            productId: newProduct.id,
            colorId: colorIds[colorData.name],
            sizeId: sizeIds[sizeData.name],
            price: data.price,
            sku: `${data.sku}-${colorData.name.toUpperCase().replace(' ', '-')}-${sizeData.value}`,
          }).returning();

          // Create inventory record
          await tx.insert(inventory).values({
            productId: newProduct.id,
            variantId: variant.id,
            quantity: data.initialStock || 0,
          });

          createdVariants++;
        }
      }

      return {
        product: newProduct,
        variantCount: createdVariants,
      };
    });

    return NextResponse.json({
      success: true,
      product: result.product,
      message: `Product created with ${result.variantCount} variants`,
    });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ 
      error: 'Failed to create product',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}