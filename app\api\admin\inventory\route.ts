import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/src/db/auth';
import { getAllInventory, updateInventoryQuantity } from '@/src/db/inventory';

export async function GET() {
  try {
    // Require admin access
    await requireAdmin();

    const inventoryData = await getAllInventory();
    return NextResponse.json({ inventory: inventoryData });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Failed to fetch inventory' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Require admin access
    await requireAdmin();

    const { inventoryId, quantity } = await request.json();

    console.log(`Admin inventory update request: ${inventoryId} -> ${quantity}`);

    if (!inventoryId || quantity === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const result = await updateInventoryQuantity(inventoryId, quantity);
    console.log('Admin inventory update successful:', result);

    return NextResponse.json({
      success: true,
      updated: result.updated,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Admin inventory update error:', error);

    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }

    return NextResponse.json({
      error: 'Failed to update inventory',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}