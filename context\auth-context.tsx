"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { createClient } from '@/utils/supabase/client';
import { User, Session } from '@supabase/supabase-js';

interface UserProfile {
  id: string;
  firstName?: string;
  lastName?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isAdmin: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAdmin: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const supabase = createClient();

  const fetchUserProfile = async (userId: string) => {
    try {
      // Add timeout to prevent hanging
      const profilePromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timed out')), 10000);
      });

      const { data: profileData, error } = await Promise.race([profilePromise, timeoutPromise]) as any;

      if (error) {
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') {
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              id: userId,
              is_admin: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (createError) {
            // Set a basic profile even if creation fails
            const basicProfile: UserProfile = {
              id: userId,
              isAdmin: false,
            };
            setProfile(basicProfile);
            setIsAdmin(false);
            return basicProfile;
          }

          const userProfile: UserProfile = {
            id: userId,
            isAdmin: false,
          };
          setProfile(userProfile);
          setIsAdmin(false);
          return userProfile;
        }

        // For other errors, set a basic profile to prevent infinite loading
        const basicProfile: UserProfile = {
          id: userId,
          isAdmin: false,
        };
        setProfile(basicProfile);
        setIsAdmin(false);
        return basicProfile;
      }

      const userProfile: UserProfile = {
        id: userId,
        firstName: profileData?.first_name || profileData?.firstName || undefined,
        lastName: profileData?.last_name || profileData?.lastName || undefined,
        addressLine1: profileData?.address_line1 || profileData?.addressLine1 || undefined,
        addressLine2: profileData?.address_line2 || profileData?.addressLine2 || undefined,
        city: profileData?.city || undefined,
        state: profileData?.state || undefined,
        postalCode: profileData?.postal_code || profileData?.postalCode || undefined,
        country: profileData?.country || undefined,
        isAdmin: profileData?.is_admin || profileData?.isAdmin || false,
        createdAt: profileData?.created_at || profileData?.createdAt || undefined,
        updatedAt: profileData?.updated_at || profileData?.updatedAt || undefined,
      };

      setProfile(userProfile);
      setIsAdmin(userProfile.isAdmin);
      return userProfile;
    } catch (error) {
      // Always set a basic profile to prevent infinite loading
      const basicProfile: UserProfile = {
        id: userId,
        isAdmin: false,
      };
      setProfile(basicProfile);
      setIsAdmin(false);
      return basicProfile;
    }
  };

  const refreshSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        return;
      }

      setSession(session);
      setUser(session?.user || null);

      if (session?.user) {
        await fetchUserProfile(session.user.id);
      } else {
        setIsAdmin(false);
        setProfile(null);
      }
    } catch (error) {
      // Silent error handling
    }
  };

  const refreshProfile = async () => {
    if (user?.id) {
      await fetchUserProfile(user.id);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user?.id) {
      throw new Error('No user logged in');
    }

    try {
      // Use snake_case for database (matching actual database columns)
      const dbUpdates: any = {};
      if (updates.firstName !== undefined) dbUpdates.first_name = updates.firstName;
      if (updates.lastName !== undefined) dbUpdates.last_name = updates.lastName;
      if (updates.addressLine1 !== undefined) dbUpdates.address_line1 = updates.addressLine1;
      if (updates.addressLine2 !== undefined) dbUpdates.address_line2 = updates.addressLine2;
      if (updates.city !== undefined) dbUpdates.city = updates.city;
      if (updates.state !== undefined) dbUpdates.state = updates.state;
      if (updates.postalCode !== undefined) dbUpdates.postal_code = updates.postalCode;
      if (updates.country !== undefined) dbUpdates.country = updates.country;

      dbUpdates.updated_at = new Date().toISOString();

      // First, check if profile exists
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      let result;
      if (!existingProfile) {
        // Profile doesn't exist, create it with the updates
        result = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            is_admin: false,
            created_at: new Date().toISOString(),
            ...dbUpdates
          });
      } else {
        // Profile exists, update it
        result = await supabase
          .from('profiles')
          .update(dbUpdates)
          .eq('id', user.id);
      }

      if (result.error) {
        throw result.error;
      }

      // Update local state
      setProfile(prev => prev ? { ...prev, ...updates } : null);
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      // Clear local state immediately
      setUser(null);
      setSession(null);
      setProfile(null);
      setIsAdmin(false);
      setIsLoading(false);

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        // Even if Supabase signOut fails, we've cleared local state
      }

      // Clear any cached data
      if (typeof window !== 'undefined') {
        // Clear localStorage items that might contain user data
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith('supabase.') || key.includes('auth'))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));

        // Clear sessionStorage as well
        sessionStorage.clear();
      }
    } catch (error) {
      // Even if there's an error, clear local state
      setUser(null);
      setSession(null);
      setProfile(null);
      setIsAdmin(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        // Add timeout to prevent hanging
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Session fetch timed out')), 10000);
        });

        const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;

        if (!mounted) return;

        if (error) {
          setIsLoading(false);
          return;
        }

        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          await fetchUserProfile(session.user.id);
        }
      } catch (error) {
        // Silent error handling
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        try {
          // Handle different auth events
          if (event === 'SIGNED_OUT') {
            setUser(null);
            setSession(null);
            setProfile(null);
            setIsAdmin(false);
            setIsLoading(false);
            return;
          }

          if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
            setSession(session);
            setUser(session?.user || null);

            if (session?.user) {
              await fetchUserProfile(session.user.id);
            }
          } else {
            // For other events, just update session and user
            setSession(session);
            setUser(session?.user || null);

            if (session?.user) {
              await fetchUserProfile(session.user.id);
            } else {
              setIsAdmin(false);
              setProfile(null);
            }
          }
        } catch (error) {
          // Silent error handling
        } finally {
          if (mounted) {
            setIsLoading(false);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Refresh session when the component mounts or when the page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshSession();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const value = {
    user,
    session,
    profile,
    isLoading,
    isAdmin,
    signOut,
    refreshSession,
    refreshProfile,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
