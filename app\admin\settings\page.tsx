"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Save, Globe, Bell, Shield, CreditCard, Mail } from "lucide-react";

export default function AdminSettings() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-semibold">Settings</h1>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Sidebar Navigation */}
        <div className="col-span-12 md:col-span-3">
          <Card className="bg-card p-2">
            <nav>
              <ul className="space-y-1">
                <li>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-left font-normal bg-sidebar-primary text-sidebar-primary-foreground"
                  >
                    <Globe className="h-4 w-4 mr-2" />
                    General
                  </Button>
                </li>
                <li>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-left font-normal"
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Notifications
                  </Button>
                </li>
                <li>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-left font-normal"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Security
                  </Button>
                </li>
                <li>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-left font-normal"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Billing
                  </Button>
                </li>
                <li>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-left font-normal"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Email Templates
                  </Button>
                </li>
              </ul>
            </nav>
          </Card>
        </div>

        {/* Main Settings Content */}
        <div className="col-span-12 md:col-span-9">
          <Card className="bg-card p-6">
            <h2 className="text-lg font-medium mb-6">General Settings</h2>
            
            <div className="space-y-6">
              {/* Store Information */}
              <div className="space-y-4">
                <h3 className="text-base font-medium">Store Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="store-name">Store Name</Label>
                    <Input 
                      id="store-name" 
                      defaultValue="Revision" 
                      className="bg-background border-input"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="store-url">Store URL</Label>
                    <Input 
                      id="store-url" 
                      defaultValue="https://revision.com" 
                      className="bg-background border-input"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="store-description">Store Description</Label>
                  <textarea 
                    id="store-description" 
                    rows={3}
                    defaultValue="Revised Outlook - Premium streetwear and accessories." 
                    className="w-full p-2 rounded-md bg-background border border-input focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
              </div>
              
              <hr className="border-neutral-800" />
              
              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-base font-medium">Contact Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contact-email">Contact Email</Label>
                    <Input 
                      id="contact-email" 
                      type="email"
                      defaultValue="<EMAIL>" 
                      className="bg-background border-input"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contact-phone">Contact Phone</Label>
                    <Input 
                      id="contact-phone" 
                      defaultValue="+****************" 
                      className="bg-background border-input"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="store-address">Store Address</Label>
                  <textarea 
                    id="store-address" 
                    rows={3}
                    defaultValue="123 Fashion Street, Suite 101&#10;San Francisco, CA 94105&#10;United States" 
                    className="w-full p-2 rounded-md bg-background border border-input focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
              </div>
              
              <hr className="border-neutral-800" />
              
              {/* Social Media */}
              <div className="space-y-4">
                <h3 className="text-base font-medium">Social Media</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input 
                      id="instagram" 
                      defaultValue="@revision_official" 
                      className="bg-background border-input"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input 
                      id="twitter" 
                      defaultValue="@revision" 
                      className="bg-background border-input"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 flex justify-end">
              <Button className="flex items-center gap-1">
                <Save className="h-4 w-4" />
                <span>Save Changes</span>
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
