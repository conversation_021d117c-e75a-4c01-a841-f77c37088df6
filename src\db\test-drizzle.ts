import { db, profiles } from './index';

async function testDrizzleConnection() {
  try {
    console.log('Testing Drizzle connection...');
    
    const allProfiles = await db.select().from(profiles).limit(5);
    console.log('Profiles found:', allProfiles.length);
    
    console.log('Dr<PERSON><PERSON> is working correctly!');
    process.exit(0);
  } catch (error) {
    console.error('Drizzle connection error:', error);
    process.exit(1);
  }
}

testDrizzleConnection();