{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "5", "dialect": "pg", "tables": {"order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_items_product_id_products_id_fk": {"name": "order_items_product_id_products_id_fk", "tableFrom": "order_items", "tableTo": "products", "schemaTo": "public", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "order_items_variant_id_product_variants_id_fk": {"name": "order_items_variant_id_product_variants_id_fk", "tableFrom": "order_items", "tableTo": "product_variants", "schemaTo": "public", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "discounts": {"name": "discounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "discount_type", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "min_order_value": {"name": "min_order_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "starts_at": {"name": "starts_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"discounts_code_unique": {"columns": ["code"], "nullsNotDistinct": false, "name": "discounts_code_unique"}}}, "orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "total": {"name": "total", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "order_status", "primaryKey": false, "notNull": true, "default": "'pending'"}, "shipping_address": {"name": "shipping_address", "type": "text", "primaryKey": false, "notNull": true}, "billing_address": {"name": "billing_address", "type": "text", "primaryKey": false, "notNull": true}, "payment_intent_id": {"name": "payment_intent_id", "type": "text", "primaryKey": false, "notNull": false}, "discount_total": {"name": "discount_total", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "shipping_total": {"name": "shipping_total", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "featured_image_url": {"name": "featured_image_url", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "product_status", "primaryKey": false, "notNull": true, "default": "'draft'"}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "sizes": {"name": "sizes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "colors": {"name": "colors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "inventory": {"name": "inventory", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"inventory_product_id_products_id_fk": {"name": "inventory_product_id_products_id_fk", "tableFrom": "inventory", "tableTo": "products", "schemaTo": "public", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inventory_variant_id_product_variants_id_fk": {"name": "inventory_variant_id_product_variants_id_fk", "tableFrom": "inventory", "tableTo": "product_variants", "schemaTo": "public", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_variants": {"name": "product_variants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "size_id": {"name": "size_id", "type": "uuid", "primaryKey": false, "notNull": false}, "color_id": {"name": "color_id", "type": "uuid", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": false}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"product_variants_color_id_colors_id_fk": {"name": "product_variants_color_id_colors_id_fk", "tableFrom": "product_variants", "tableTo": "colors", "schemaTo": "public", "columnsFrom": ["color_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_variants_product_id_products_id_fk": {"name": "product_variants_product_id_products_id_fk", "tableFrom": "product_variants", "tableTo": "products", "schemaTo": "public", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_variants_size_id_sizes_id_fk": {"name": "product_variants_size_id_sizes_id_fk", "tableFrom": "product_variants", "tableTo": "sizes", "schemaTo": "public", "columnsFrom": ["size_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "profiles": {"name": "profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address_line1": {"name": "address_line1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line2": {"name": "address_line2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"key_status": {"name": "key_status", "values": {"default": "default", "valid": "valid", "invalid": "invalid", "expired": "expired"}}, "key_type": {"name": "key_type", "values": {"aead-ietf": "aead-ietf", "aead-det": "aead-det", "hmacsha512": "hmacsha512", "hmacsha256": "hmacsha256", "auth": "auth", "shorthash": "<PERSON><PERSON>h", "generichash": "<PERSON><PERSON><PERSON>", "kdf": "kdf", "secretbox": "secretbox", "secretstream": "secretstream", "stream_xchacha20": "stream_xchacha20"}}, "aal_level": {"name": "aal_level", "values": {"aal1": "aal1", "aal2": "aal2", "aal3": "aal3"}}, "code_challenge_method": {"name": "code_challenge_method", "values": {"s256": "s256", "plain": "plain"}}, "factor_status": {"name": "factor_status", "values": {"unverified": "unverified", "verified": "verified"}}, "factor_type": {"name": "factor_type", "values": {"totp": "totp", "webauthn": "webauthn", "phone": "phone"}}, "one_time_token_type": {"name": "one_time_token_type", "values": {"confirmation_token": "confirmation_token", "reauthentication_token": "reauthentication_token", "recovery_token": "recovery_token", "email_change_token_new": "email_change_token_new", "email_change_token_current": "email_change_token_current", "phone_change_token": "phone_change_token"}}, "discount_type": {"name": "discount_type", "values": {"percentage": "percentage", "fixed": "fixed"}}, "order_status": {"name": "order_status", "values": {"pending": "pending", "processing": "processing", "shipped": "shipped", "delivered": "delivered", "cancelled": "cancelled", "refunded": "refunded"}}, "product_status": {"name": "product_status", "values": {"draft": "draft", "active": "active", "archived": "archived"}}, "action": {"name": "action", "values": {"INSERT": "INSERT", "UPDATE": "UPDATE", "DELETE": "DELETE", "TRUNCATE": "TRUNCATE", "ERROR": "ERROR"}}, "equality_op": {"name": "equality_op", "values": {"eq": "eq", "neq": "neq", "lt": "lt", "lte": "lte", "gt": "gt", "gte": "gte", "in": "in"}}}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}