'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/context/auth-context'

export default function AccountSettings() {
  const { user, profile, isLoading, updateProfile, refreshProfile } = useAuth()
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [saving, setSaving] = useState(false)
  const [loadingTimeout, setLoadingTimeout] = useState(false)

  // Fallback timeout to prevent infinite loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        setLoadingTimeout(true)
      }
    }, 15000) // 15 second timeout

    return () => clearTimeout(timer)
  }, [isLoading])

  // Update form fields when profile data is loaded
  useEffect(() => {
    if (profile) {
      setFirstName(profile.firstName || '')
      setLastName(profile.lastName || '')
    }
  }, [profile, user, isLoading])
  
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)

    if (!user) {
      setMessage({ type: 'error', text: 'No user logged in' })
      return
    }

    // Validate input
    if (!firstName.trim() || !lastName.trim()) {
      setMessage({ type: 'error', text: 'First name and last name are required' })
      return
    }

    try {
      setSaving(true)

      // Add timeout to prevent infinite hanging
      const updatePromise = updateProfile({
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      })

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Update timed out after 30 seconds')), 30000)
      })

      await Promise.race([updatePromise, timeoutPromise])

      // Refresh profile data to ensure UI is in sync
      await refreshProfile()

      setMessage({ type: 'success', text: 'Profile updated successfully' })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Error updating profile'
      setMessage({ type: 'error', text: errorMessage })
    } finally {
      setSaving(false)
    }
  }
  
  if (isLoading && !loadingTimeout) {
    return (
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="mt-2">Loading profile...</p>
      </div>
    )
  }

  // Show timeout message if loading took too long
  if (loadingTimeout && !user) {
    return (
      <div className="text-center text-white">
        <p className="text-red-400">Loading timed out. Please refresh the page or try logging in again.</p>
      </div>
    )
  }

  if (!user) {
    return <div className="text-center text-white">Please log in to access settings.</div>
  }
  
  return (
    <div className="max-w-xl mx-auto">
        <h2 className="text-xl font-medium mb-6 text-white">Account Information</h2>
      
      {message && (
        <div 
          className={`p-3 mb-6 ${
            message.type === 'success' ? 'bg-green-900/30 border border-green-800 text-green-200' : 
                                         'bg-red-900/30 border border-red-800 text-red-200'
          }`}
        >
          {message.text}
        </div>
      )}
      
      <form onSubmit={handleUpdateProfile} className="space-y-6">
        <div>
          <Label htmlFor="email" className="text-white">Email address</Label>
          <Input
            id="email"
            type="email"
            value={user.email || ''}
            disabled
            className="mt-1 bg-neutral-800 border-neutral-700 text-neutral-400 rounded-none"
          />
          <p className="mt-1 text-xs text-neutral-400">Your email address cannot be changed</p>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName" className="text-white">First name</Label>
            <Input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
              placeholder="First name"
            />
          </div>
          <div>
            <Label htmlFor="lastName" className="text-white">Last name</Label>
            <Input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
              placeholder="Last name"
            />
          </div>
        </div>
        
        <Button
          type="submit"
          disabled={saving || !firstName.trim() || !lastName.trim()}
          className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4 w-full md:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </form>
    </div>
  )
}