"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Filter, Plus, Edit, Trash } from "lucide-react";

export default function ProductsManagement() {
  // Placeholder data for products
  const products = [
    { id: "PRD-001", name: "Black T-Shirt", category: "Apparel", price: "$25.00", stock: 120, status: "Active" },
    { id: "PRD-002", name: "White Hoodie", category: "Apparel", price: "$50.00", stock: 85, status: "Active" },
    { id: "PRD-003", name: "Denim Jacket", category: "Apparel", price: "$80.00", stock: 64, status: "Active" },
    { id: "PRD-004", name: "Canvas Tote", category: "Accessories", price: "$30.00", stock: 92, status: "Active" },
    { id: "PRD-005", name: "Logo Cap", category: "Accessories", price: "$25.00", stock: 110, status: "Active" },
    { id: "PRD-006", name: "Leather Wallet", category: "Accessories", price: "$45.00", stock: 38, status: "Low Stock" },
    { id: "PRD-007", name: "Graphic Print Tee", category: "Apparel", price: "$35.00", stock: 75, status: "Active" },
    { id: "PRD-008", name: "Cargo Pants", category: "Apparel", price: "$65.00", stock: 42, status: "Low Stock" },
    { id: "PRD-009", name: "Beanie", category: "Accessories", price: "$20.00", stock: 0, status: "Out of Stock" },
    { id: "PRD-010", name: "Sunglasses", category: "Accessories", price: "$55.00", stock: 28, status: "Low Stock" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-semibold">Products Management</h1>
        <Button size="sm" className="flex items-center gap-1">
          <Plus className="h-4 w-4" />
          <span>Add Product</span>
        </Button>
      </div>

      <Card className="bg-card">
        <div className="p-4 border-b border-neutral-800 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search products..."
              className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
            />
          </div>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </Button>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-xs text-muted-foreground border-b border-neutral-800">
                <th className="px-4 py-3">ID</th>
                <th className="px-4 py-3">Product</th>
                <th className="px-4 py-3">Category</th>
                <th className="px-4 py-3 text-right">Price</th>
                <th className="px-4 py-3 text-right">Stock</th>
                <th className="px-4 py-3">Status</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.map((product) => (
                <tr key={product.id} className="border-b border-neutral-800 last:border-0 hover:bg-muted/5">
                  <td className="px-4 py-3 text-sm">{product.id}</td>
                  <td className="px-4 py-3 text-sm font-medium">{product.name}</td>
                  <td className="px-4 py-3 text-sm">{product.category}</td>
                  <td className="px-4 py-3 text-sm text-right">{product.price}</td>
                  <td className="px-4 py-3 text-sm text-right">{product.stock}</td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                      product.status === "Active" 
                        ? "bg-green-500/10 text-green-500" 
                        : product.status === "Low Stock" 
                        ? "bg-yellow-500/10 text-yellow-500" 
                        : "bg-red-500/10 text-red-500"
                    }`}>
                      {product.status}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500 hover:text-red-600">
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="p-4 border-t border-neutral-800 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">1</span> to <span className="font-medium">10</span> of <span className="font-medium">24</span> products
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm">Next</Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
