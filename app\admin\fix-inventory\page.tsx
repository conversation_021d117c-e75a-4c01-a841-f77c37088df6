'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface InventoryRecord {
  color: string;
  size: string;
  variantId: string;
  inventoryId: string;
}

interface InventoryItem {
  color: string;
  size?: string;
  quantity: number;
  inventoryId: string;
}

interface FixResult {
  success: boolean;
  message: string;
  summary: {
    totalProperVariants: number;
    variantsWithoutInventory: number;
    recordsCreated: number;
    errors: number;
  };
  createdRecords: InventoryRecord[];
  errors: string[];
  currentInventory: {
    proper: InventoryItem[];
    legacy: InventoryItem[];
  };
  timestamp: string;
}

export default function FixInventoryPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<FixResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFixInventory = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/admin/fix-inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fix inventory');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Fix Inventory Synchronization</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ Admin Tool</h2>
        <p className="text-yellow-700">
          This tool fixes missing inventory records for product variants that exist but don&apos;t have corresponding inventory entries.
          This resolves the issue where certain variants don&apos;t appear on the frontend despite existing in the database.
        </p>
      </div>

      <Button 
        onClick={handleFixInventory} 
        disabled={isLoading}
        className="mb-6"
      >
        {isLoading ? 'Fixing Inventory...' : 'Fix Missing Inventory Records'}
      </Button>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">❌ Error</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {result && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-800 mb-4">✅ Fix Completed</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Summary:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Total proper variants: {result.summary.totalProperVariants}</li>
                <li>Variants without inventory: {result.summary.variantsWithoutInventory}</li>
                <li>Records created: {result.summary.recordsCreated}</li>
                <li>Errors: {result.summary.errors}</li>
              </ul>
            </div>

            {result.createdRecords.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2">Created Inventory Records:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {result.createdRecords.map((record: InventoryRecord, index: number) => (
                    <li key={index}>
                      {record.color} {record.size} (Inventory ID: {record.inventoryId})
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {result.errors.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2 text-red-700">Errors:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                  {result.errors.map((errorMsg: string, index: number) => (
                    <li key={index}>{errorMsg}</li>
                  ))}
                </ul>
              </div>
            )}

            <div>
              <h4 className="font-semibold mb-2">Current Inventory Status:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Proper Variants:</h5>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    {result.currentInventory.proper.map((item: InventoryItem, index: number) => (
                      <li key={index}>
                        {item.color} {item.size}: {item.quantity} units
                      </li>
                    ))}
                  </ul>
                </div>
                
                {result.currentInventory.legacy.length > 0 && (
                  <div>
                    <h5 className="font-medium mb-2">Legacy Variants (hidden from frontend):</h5>
                    <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                      {result.currentInventory.legacy.map((item: InventoryItem, index: number) => (
                        <li key={index}>
                          {item.color} (no size): {item.quantity} units
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
