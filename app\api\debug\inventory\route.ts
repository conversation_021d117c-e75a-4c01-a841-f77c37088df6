import { NextResponse } from 'next/server';
import { db } from '@/src/db/index';
import { inventory, products, productVariants, colors, sizes } from '@/src/db/schema';
import { eq } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('🔍 Starting inventory debug analysis...');

    // 1. Find the product
    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    if (!product || product.length === 0) {
      return NextResponse.json({ error: 'Product "Cropped Zip Up Hoodie" not found' }, { status: 404 });
    }

    const productInfo = {
      id: product[0].id,
      name: product[0].name,
      sku: product[0].sku,
      status: product[0].status,
      createdAt: product[0].createdAt
    };

    // 2. Get all colors and sizes
    const allColors = await db.select().from(colors);
    const allSizes = await db.select().from(sizes);

    // 3. Get all product variants for this product
    const variants = await db
      .select({
        variantId: productVariants.id,
        productId: productVariants.productId,
        colorId: productVariants.colorId,
        sizeId: productVariants.sizeId,
        colorName: colors.name,
        sizeName: sizes.name,
        sku: productVariants.sku,
        price: productVariants.price,
        createdAt: productVariants.createdAt
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(productVariants.productId, product[0].id));

    // 4. Get all inventory records for this product
    const inventoryRecords = await db
      .select({
        inventoryId: inventory.id,
        productId: inventory.productId,
        variantId: inventory.variantId,
        quantity: inventory.quantity,
        updatedAt: inventory.updatedAt,
        createdAt: inventory.createdAt,
        colorName: colors.name,
        sizeName: sizes.name,
        variantExists: productVariants.id
      })
      .from(inventory)
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id));

    // 5. Analyze problematic vs working variants
    const problematicVariants = [
      { color: 'Rustic Black', size: 'Small' },
      { color: 'Matcha Green', size: 'XL' },
      { color: 'Rustic Black', size: 'Large' }
    ];

    const workingVariants = [
      { color: 'Matcha Green', size: 'Small' },
      { color: 'Matcha Green', size: 'Medium' },
      { color: 'Matcha Green', size: 'Large' }
    ];

    const problematicAnalysis = problematicVariants.map(variant => {
      const variantRecord = variants.find(v => v.colorName === variant.color && v.sizeName === variant.size);
      const inventoryRecord = inventoryRecords.find(i => i.colorName === variant.color && i.sizeName === variant.size);
      
      return {
        color: variant.color,
        size: variant.size,
        variantExists: !!variantRecord,
        variantId: variantRecord?.variantId || null,
        variantCreatedAt: variantRecord?.createdAt || null,
        inventoryExists: !!inventoryRecord,
        inventoryId: inventoryRecord?.inventoryId || null,
        variantIdInInventory: inventoryRecord?.variantId || null,
        quantity: inventoryRecord?.quantity || null,
        inventoryUpdatedAt: inventoryRecord?.updatedAt || null,
        inventoryCreatedAt: inventoryRecord?.createdAt || null,
        hasVariantLink: inventoryRecord?.variantExists ? true : false
      };
    });

    const workingAnalysis = workingVariants.map(variant => {
      const variantRecord = variants.find(v => v.colorName === variant.color && v.sizeName === variant.size);
      const inventoryRecord = inventoryRecords.find(i => i.colorName === variant.color && i.sizeName === variant.size);
      
      return {
        color: variant.color,
        size: variant.size,
        variantExists: !!variantRecord,
        variantId: variantRecord?.variantId || null,
        variantCreatedAt: variantRecord?.createdAt || null,
        inventoryExists: !!inventoryRecord,
        inventoryId: inventoryRecord?.inventoryId || null,
        variantIdInInventory: inventoryRecord?.variantId || null,
        quantity: inventoryRecord?.quantity || null,
        inventoryUpdatedAt: inventoryRecord?.updatedAt || null,
        inventoryCreatedAt: inventoryRecord?.createdAt || null,
        hasVariantLink: inventoryRecord?.variantExists ? true : false
      };
    });

    // 6. Check for orphaned inventory records
    const orphanedInventory = inventoryRecords.filter(record => !record.variantId);
    
    // 7. Check for variants without inventory
    const variantsWithoutInventory = variants.filter(variant => 
      !inventoryRecords.some(inv => inv.variantId === variant.variantId)
    );

    // 8. Test the actual getProductInventory function
    const { getProductInventory } = await import('@/src/db/inventory');
    let inventoryFunctionResult;
    try {
      inventoryFunctionResult = await getProductInventory('Cropped Zip Up Hoodie');
    } catch (error) {
      inventoryFunctionResult = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    const debugData = {
      timestamp: new Date().toISOString(),
      product: productInfo,
      colors: allColors,
      sizes: allSizes,
      variants: variants,
      inventoryRecords: inventoryRecords,
      problematicVariants: problematicAnalysis,
      workingVariants: workingAnalysis,
      orphanedInventory: orphanedInventory,
      variantsWithoutInventory: variantsWithoutInventory,
      inventoryFunctionResult: inventoryFunctionResult,
      summary: {
        totalVariants: variants.length,
        totalInventoryRecords: inventoryRecords.length,
        orphanedRecords: orphanedInventory.length,
        variantsWithoutInventory: variantsWithoutInventory.length
      }
    };

    return NextResponse.json(debugData, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json({
      error: 'Debug analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
