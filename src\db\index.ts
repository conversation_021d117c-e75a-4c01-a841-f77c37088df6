import { drizzle } from 'drizzle-orm/postgres-js';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

type DbType = PostgresJsDatabase<typeof schema>;

let dbInstance: DbType | null = null;
let connectionAttempted = false;

function createDbConnection(): DbType {
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    throw new Error('DATABASE_URL environment variable is required');
  }
  
  const client = postgres(connectionString, { 
    prepare: false,
    ssl: 'require' 
  });
  
  return drizzle(client, { schema });
}

// Lazy-load database connection
export const db = new Proxy({} as DbType, {
  get(target, prop) {
    if (!dbInstance && !connectionAttempted) {
      connectionAttempted = true;
      try {
        dbInstance = createDbConnection();
      } catch (error) {
        connectionAttempted = false;
        throw error;
      }
    }
    
    if (!dbInstance) {
      throw new Error('Database connection not initialized');
    }
    
    return dbInstance[prop as keyof DbType];
  }
});

export * from './schema';