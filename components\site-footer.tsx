import Link from "next/link";

export function SiteFooter() {
  return (
    <footer className="border-t border-neutral-800 py-8 md:py-12 bg-neutral-900 text-white">
      <div className="container mx-auto px-4 md:px-8">
        {/* Main footer content */}
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between mb-8">
            {/* REVISION - Left aligned */}
            <div className="md:w-1/4 mb-8 md:mb-0">
              <Link href="/" className="inline-block">
                <h3 className="text-lg font-bold tracking-tight text-white">REVISION</h3>
              </Link>
              <p className="mt-2 text-sm text-gray-400">Revised Outlook</p>
              <div className="mt-4 flex space-x-4">
                <Link href="https://www.instagram.com/revision.ro/#" className="text-gray-400 hover:text-white transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </Link>
              </div>
            </div>
            
            {/* SHOP & ABOUT - Center aligned */}
            <div className="flex flex-col md:flex-row space-y-8 md:space-y-0 md:justify-between md:flex-1 px-8 md:px-16 lg:px-24">
              <div>
                <h4 className="text-sm font-medium uppercase mb-4 text-white">Shop</h4>
                <nav className="flex flex-col space-y-2">
                  <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                    Bestsellers
                  </Link>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                    Collections
                  </Link>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                    Accessories
                  </Link>
                </nav>
              </div>
              
              <div>
                <h4 className="text-sm font-medium uppercase mb-4 text-white">About</h4>
                <nav className="flex flex-col space-y-2">
                  <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                    Our Story
                  </Link>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                    Journal
                  </Link>
                </nav>
              </div>
            </div>
            
            {/* CUSTOMER CARE - Right aligned */}
            <div className="md:w-1/4 md:text-right">
              <h4 className="text-sm font-medium uppercase mb-4 text-white">Customer Care</h4>
              <nav className="flex flex-col space-y-2 md:items-end">
                <Link href="/contact" className="text-sm text-gray-400 hover:text-white transition-colors">
                  Contact Us
                </Link>
                <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                  Shipping & Returns
                </Link>
                <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                  FAQs
                </Link>
                <Link href="#" className="text-sm text-gray-400 hover:text-white transition-colors">
                  Size Guide
                </Link>
              </nav>
            </div>
          </div>
        </div>
        
        {/* Divider line */}
        <div className="border-t border-neutral-800 max-w-6xl mx-auto"></div>
        
        {/* Sub-footer with copyright and links */}
        <div className="max-w-6xl mx-auto pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div>
              <p className="text-xs text-gray-400">
                &copy; {new Date().getFullYear()} Revision. All rights reserved.
              </p>
            </div>
            <div className="flex flex-wrap items-center space-x-4 text-xs text-gray-400 mt-4 md:mt-0">
              <Link href="#" className="hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="#" className="hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="#" className="hover:text-white transition-colors">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 