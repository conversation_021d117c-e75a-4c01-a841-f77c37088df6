'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
  size?: string
  color?: string
}

export interface Order {
  id: string
  date: string
  status: 'processing' | 'shipped' | 'delivered' | 'cancelled'
  total: number
  items: OrderItem[]
  trackingNumber?: string
}

interface OrderItemProps {
  order: Order
}

export function OrderItem({ order }: OrderItemProps) {
  const [expanded, setExpanded] = useState(false)
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
  
  const getStatusBadgeClass = (status: Order['status']) => {
    switch (status) {
      case 'processing':
        return 'bg-blue-900/30 text-blue-200 border border-blue-800'
      case 'shipped':
        return 'bg-yellow-900/30 text-yellow-200 border border-yellow-800'
      case 'delivered':
        return 'bg-green-900/30 text-green-200 border border-green-800'
      case 'cancelled':
        return 'bg-red-900/30 text-red-200 border border-red-800'
      default:
        return 'bg-gray-900/30 text-gray-200 border border-gray-800'
    }
  }
  
  return (
    <div className="bg-neutral-800 border border-neutral-700 mb-4">
      <div className="p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <p className="text-sm text-gray-400 mb-1">Order #{order.id}</p>
          <p className="text-white font-medium">{formatDate(order.date)}</p>
        </div>
        
        <div>
          <span className={`text-xs px-2 py-1 rounded-sm ${getStatusBadgeClass(order.status)}`}>
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </span>
        </div>
        
        <div className="text-right">
          <p className="text-sm text-gray-400 mb-1">Total</p>
          <p className="text-white font-medium">{formatCurrency(order.total)}</p>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setExpanded(!expanded)}
          className="text-sm text-gray-300 hover:text-white"
        >
          {expanded ? 'Hide' : 'View'} Details
        </Button>
      </div>
      
      {expanded && (
        <div className="p-4 border-t border-neutral-700">
          <div className="mb-4">
            <h4 className="text-white text-sm font-medium mb-2">Items</h4>
            <div className="space-y-3">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center gap-3">
                  <div className="h-16 w-16 bg-neutral-700 flex-shrink-0">
                    {/* Placeholder for product image */}
                    <div className="h-full w-full flex items-center justify-center text-gray-500 text-xs">
                      Image
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-white">{item.name}</p>
                    <div className="text-gray-400 text-sm">
                      {item.size && <span className="mr-2">Size: {item.size}</span>}
                      {item.color && <span>Color: {item.color}</span>}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {formatCurrency(item.price)} × {item.quantity}
                    </div>
                  </div>
                  <div className="text-white text-right">
                    {formatCurrency(item.price * item.quantity)}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {order.trackingNumber && (
            <div className="mb-4">
              <h4 className="text-white text-sm font-medium mb-1">Tracking Number</h4>
              <p className="text-gray-300">{order.trackingNumber}</p>
            </div>
          )}
          
          {order.status === 'shipped' && (
            <Button
              variant="outline"
              size="sm"
              className="border border-neutral-600 text-white hover:bg-neutral-700 hover:text-white mt-2"
              onClick={() => window.open(`https://track.example.com/${order.trackingNumber}`, '_blank')}
            >
              Track Order
            </Button>
          )}
        </div>
      )}
    </div>
  )
}