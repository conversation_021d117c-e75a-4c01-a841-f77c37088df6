"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, Filter, AlertTriangle, ArrowUpDown, Plus, X } from "lucide-react";

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  sku: string | null;
  variantId: string | null;
  variantSku: string | null;
  colorName: string | null;
  colorValue: string | null;
  sizeName: string | null;
  sizeValue: string | null;
  quantity: number;
  updatedAt: string;
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  sku: string;
  featuredImageUrl: string;
  colors: { name: string; value: string; selected: boolean }[];
  sizes: { name: string; value: string; selected: boolean }[];
  initialStock: number;
  newColorName: string;
  newColorValue: string;
  newSizeName: string;
  newSizeValue: string;
}

export default function InventoryManagement() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editQuantity, setEditQuantity] = useState<number>(0);
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [addingProduct, setAddingProduct] = useState(false);
  const [productForm, setProductForm] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    sku: '',
    featuredImageUrl: '',
    colors: [
      { name: 'Rustic Black', value: '#121212', selected: false },
      { name: 'Sky Blue', value: '#87CEEB', selected: false },
      { name: 'Matcha Green', value: '#8DC73F', selected: false },
    ],
    sizes: [
      { name: 'Small', value: 'S', selected: false },
      { name: 'Medium', value: 'M', selected: false },
      { name: 'Large', value: 'L', selected: false },
      { name: 'XL', value: 'XL', selected: false },
    ],
    initialStock: 0,
    newColorName: '',
    newColorValue: '',
    newSizeName: '',
    newSizeValue: '',
  });

  useEffect(() => {
    fetchInventory();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchInventory = async () => {
    try {
      const response = await fetch('/api/admin/inventory');
      if (!response.ok) throw new Error('Failed to fetch inventory');
      const data = await response.json();
      const sortedInventory = sortInventoryBySKU(data.inventory || []);
      setInventoryItems(sortedInventory);
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  const sortInventoryBySKU = (items: InventoryItem[]): InventoryItem[] => {
    return items.sort((a, b) => {
      const skuA = (a.variantSku || a.sku || '').toUpperCase();
      const skuB = (b.variantSku || b.sku || '').toUpperCase();
      
      // Primary sort by SKU prefix (e.g., "HOODIE", "SHIRT")
      const prefixA = skuA.split('-')[0];
      const prefixB = skuB.split('-')[0];
      
      if (prefixA !== prefixB) {
        return prefixA.localeCompare(prefixB);
      }
      
      // If same prefix, sort by full SKU
      const skuCompare = skuA.localeCompare(skuB);
      if (skuCompare !== 0) return skuCompare;
      
      // Secondary sort by size order within same SKU group
      const sizeOrder = ['S', 'M', 'L', 'XL', 'XXL'];
      const sizeIndexA = sizeOrder.indexOf(a.sizeValue || '');
      const sizeIndexB = sizeOrder.indexOf(b.sizeValue || '');
      
      if (sizeIndexA !== -1 && sizeIndexB !== -1) {
        return sizeIndexA - sizeIndexB;
      }
      
      // Fallback to alphabetical sort by size name
      return (a.sizeName || '').localeCompare(b.sizeName || '');
    });
  };

  const getSkuGroup = (item: InventoryItem): string => {
    const sku = item.variantSku || item.sku || '';
    // Extract the base SKU without size suffix (e.g., "HOODIE-BLK" from "HOODIE-BLK-S")
    const parts = sku.split('-');
    if (parts.length >= 2) {
      return parts.slice(0, -1).join('-');
    }
    return sku;
  };

  const updateInventory = async (inventoryId: string, quantity: number) => {
    try {
      const response = await fetch('/api/admin/inventory', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ inventoryId, quantity }),
      });
      
      if (!response.ok) throw new Error('Failed to update inventory');
      
      // Update the item locally first for immediate feedback
      setInventoryItems(prev => 
        prev.map(item => 
          item.id === inventoryId ? { ...item, quantity } : item
        )
      );
      
      setEditingId(null);
      
      // Then fetch the latest data to ensure consistency
      await fetchInventory();
    } catch (error) {
      console.error('Error updating inventory:', error);
    }
  };

  const handleAddProduct = async () => {
    if (!productForm.name || !productForm.price || !productForm.sku) {
      alert('Please fill in all required fields');
      return;
    }

    const selectedColors = productForm.colors.filter(c => c.selected);
    const selectedSizes = productForm.sizes.filter(s => s.selected);

    if (selectedColors.length === 0 || selectedSizes.length === 0) {
      alert('Please select at least one color and one size');
      return;
    }

    setAddingProduct(true);
    try {
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: productForm.name,
          description: productForm.description,
          price: productForm.price,
          sku: productForm.sku,
          featuredImageUrl: productForm.featuredImageUrl || '/assets/all-top.JPG',
          colors: selectedColors,
          sizes: selectedSizes,
          initialStock: productForm.initialStock,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add product');
      }

      // Reset form
      setProductForm({
        name: '',
        description: '',
        price: '',
        sku: '',
        featuredImageUrl: '',
        colors: productForm.colors.map(c => ({ ...c, selected: false })),
        sizes: productForm.sizes.map(s => ({ ...s, selected: false })),
        initialStock: 0,
        newColorName: '',
        newColorValue: '',
        newSizeName: '',
        newSizeValue: '',
      });
      setShowAddProduct(false);
      await fetchInventory();
    } catch (error) {
      console.error('Error adding product:', error);
      alert(error instanceof Error ? error.message : 'Failed to add product');
    } finally {
      setAddingProduct(false);
    }
  };

  const getStatus = (quantity: number) => {
    if (quantity === 0) return "Out of Stock";
    if (quantity < 20) return "Low Stock";
    return "In Stock";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const lowStockCount = inventoryItems.filter(item => item.quantity > 0 && item.quantity < 20).length;
  const outOfStockCount = inventoryItems.filter(item => item.quantity === 0).length;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-semibold">Inventory Management</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </Button>
          <Button 
            size="sm" 
            onClick={() => setShowAddProduct(true)}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            <span>Add Product</span>
          </Button>
        </div>
      </div>

      {/* Low Stock Alert */}
      {(lowStockCount > 0 || outOfStockCount > 0) && (
        <Card className="bg-yellow-500/10 border border-yellow-500/20 p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <div>
              <h3 className="font-medium text-yellow-500">Stock Alert</h3>
              <p className="text-sm text-muted-foreground">
                {outOfStockCount > 0 && `${outOfStockCount} product${outOfStockCount > 1 ? 's' : ''} out of stock. `}
                {lowStockCount > 0 && `${lowStockCount} product${lowStockCount > 1 ? 's' : ''} running low on stock.`}
              </p>
            </div>
            <Button variant="outline" size="sm" className="ml-auto">View All</Button>
          </div>
        </Card>
      )}

      <Card className="bg-card">
        <div className="p-4 border-b border-neutral-800 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search inventory..."
              className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center text-muted-foreground">Loading inventory...</div>
          ) : inventoryItems.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">No inventory items found</div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="text-left text-xs text-muted-foreground border-b border-neutral-800">
                  <th className="px-4 py-3">
                    <div className="flex items-center">
                      <span>Product</span>
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </div>
                  </th>
                  <th className="px-4 py-3">Color</th>
                  <th className="px-4 py-3">Size</th>
                  <th className="px-4 py-3">
                    <div className="flex items-center">
                      <span>SKU</span>
                      <ArrowUpDown className="ml-1 h-3 w-3 text-primary" />
                    </div>
                  </th>
                  <th className="px-4 py-3 text-right">
                    <div className="flex items-center justify-end">
                      <span>Stock</span>
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </div>
                  </th>
                  <th className="px-4 py-3">Status</th>
                  <th className="px-4 py-3">Last Updated</th>
                  <th className="px-4 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {inventoryItems.map((item, index) => {
                  const status = getStatus(item.quantity);
                  const currentGroup = getSkuGroup(item);
                  const previousGroup = index > 0 ? getSkuGroup(inventoryItems[index - 1]) : null;
                  const nextGroup = index < inventoryItems.length - 1 ? getSkuGroup(inventoryItems[index + 1]) : null;
                  const isGroupStart = currentGroup !== previousGroup;
                  const isGroupEnd = currentGroup !== nextGroup;
                  
                  // Determine background color for visual grouping
                  let groupIndex = 0;
                  for (let i = 0; i <= index; i++) {
                    if (i === 0 || getSkuGroup(inventoryItems[i]) !== getSkuGroup(inventoryItems[i - 1])) {
                      groupIndex++;
                    }
                  }
                  const isEvenGroup = groupIndex % 2 === 0;
                  
                  return (
                    <tr 
                      key={item.id} 
                      className={`
                        hover:bg-muted/5 transition-colors
                        ${isEvenGroup ? 'bg-neutral-900/30' : ''}
                        ${isGroupStart ? 'border-t-2 border-neutral-700' : ''}
                        ${isGroupEnd ? 'border-b border-neutral-700' : 'border-b border-neutral-800'}
                      `}
                    >
                      <td className={`px-4 py-3 text-sm font-medium ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {item.productName}
                      </td>
                      <td className={`px-4 py-3 text-sm ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {item.colorName && (
                          <div className="flex items-center gap-2">
                            <span 
                              className="w-4 h-4 rounded-full border border-neutral-600" 
                              style={{ backgroundColor: item.colorValue || '#000' }}
                            />
                            <span>{item.colorName}</span>
                          </div>
                        )}
                      </td>
                      <td className={`px-4 py-3 text-sm ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {item.sizeName || '-'}
                      </td>
                      <td className={`px-4 py-3 text-sm text-muted-foreground ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        <div className="flex items-center gap-2">
                          {item.variantSku || item.sku || '-'}
                          {isGroupStart && (
                            <span className="text-xs bg-neutral-700 px-2 py-0.5 rounded-full">
                              Group: {currentGroup}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className={`px-4 py-3 text-sm text-right ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {editingId === item.id ? (
                          <input
                            type="number"
                            value={editQuantity}
                            onChange={(e) => setEditQuantity(Number(e.target.value))}
                            className="w-20 px-2 py-1 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
                            min="0"
                          />
                        ) : (
                          item.quantity
                        )}
                      </td>
                      <td className={`px-4 py-3 text-sm ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                          status === "In Stock" 
                            ? "bg-green-500/10 text-green-500" 
                            : status === "Low Stock" 
                            ? "bg-yellow-500/10 text-yellow-500" 
                            : "bg-red-500/10 text-red-500"
                        }`}>
                          {status}
                        </span>
                      </td>
                      <td className={`px-4 py-3 text-sm ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {formatDate(item.updatedAt)}
                      </td>
                      <td className={`px-4 py-3 text-sm text-right ${isGroupStart ? 'pt-4' : ''} ${isGroupEnd ? 'pb-4' : ''}`}>
                        {editingId === item.id ? (
                          <div className="flex gap-2 justify-end">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => updateInventory(item.id, editQuantity)}
                            >
                              Save
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => setEditingId(null)}
                            >
                              Cancel
                            </Button>
                          </div>
                        ) : (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => {
                              setEditingId(item.id);
                              setEditQuantity(item.quantity);
                            }}
                          >
                            Adjust
                          </Button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
        <div className="p-4 border-t border-neutral-800 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{inventoryItems.length > 0 ? 1 : 0}</span> to <span className="font-medium">{inventoryItems.length}</span> of <span className="font-medium">{inventoryItems.length}</span> items
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </div>
      </Card>

      {/* Add Product Modal */}
      {showAddProduct && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="bg-card w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Add New Product</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddProduct(false)}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    value={productForm.name}
                    onChange={(e) => setProductForm({ ...productForm, name: e.target.value })}
                    placeholder="e.g., Classic T-Shirt"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <textarea
                    id="description"
                    value={productForm.description}
                    onChange={(e) => setProductForm({ ...productForm, description: e.target.value })}
                    placeholder="Product description..."
                    className="mt-1 w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring min-h-[80px]"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price">Price *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={productForm.price}
                      onChange={(e) => setProductForm({ ...productForm, price: e.target.value })}
                      placeholder="0.00"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sku">SKU *</Label>
                    <Input
                      id="sku"
                      value={productForm.sku}
                      onChange={(e) => setProductForm({ ...productForm, sku: e.target.value })}
                      placeholder="e.g., TSH-001"
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="image">Featured Image URL</Label>
                  <Input
                    id="image"
                    value={productForm.featuredImageUrl}
                    onChange={(e) => setProductForm({ ...productForm, featuredImageUrl: e.target.value })}
                    placeholder="/assets/product-image.jpg"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label>Colors *</Label>
                  <div className="mt-2 flex gap-3">
                    {productForm.colors.map((color, index) => (
                      <label
                        key={color.name}
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={color.selected}
                          onChange={(e) => {
                            const newColors = [...productForm.colors];
                            newColors[index].selected = e.target.checked;
                            setProductForm({ ...productForm, colors: newColors });
                          }}
                          className="rounded border-gray-300"
                        />
                        <div className="flex items-center gap-2">
                          <span
                            className="w-4 h-4 rounded-full border border-neutral-600"
                            style={{ backgroundColor: color.value }}
                          />
                          <span className="text-sm">{color.name}</span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Sizes *</Label>
                  <div className="mt-2 grid grid-cols-4 gap-2">
                    {productForm.sizes.map((size, index) => (
                      <label
                        key={size.name}
                        className={`
                          flex items-center justify-center p-2 border rounded cursor-pointer
                          ${size.selected
                            ? 'bg-white text-neutral-900 border-white'
                            : 'text-gray-300 border-neutral-700 hover:border-gray-500'
                          }
                        `}
                      >
                        <input
                          type="checkbox"
                          checked={size.selected}
                          onChange={(e) => {
                            const newSizes = [...productForm.sizes];
                            newSizes[index].selected = e.target.checked;
                            setProductForm({ ...productForm, sizes: newSizes });
                          }}
                          className="sr-only"
                        />
                        <span className="text-sm">{size.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="stock">Initial Stock (per variant)</Label>
                  <Input
                    id="stock"
                    type="number"
                    value={productForm.initialStock}
                    onChange={(e) => setProductForm({ ...productForm, initialStock: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    This amount will be set for each color/size combination
                  </p>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    onClick={handleAddProduct}
                    disabled={addingProduct}
                    className="flex-1"
                  >
                    {addingProduct ? 'Adding...' : 'Add Product'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAddProduct(false)}
                    disabled={addingProduct}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
