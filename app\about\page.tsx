import React from 'react';
import Image from 'next/image';
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";

// Line Drawing Animation component (reused from homepage)
const LineDrawingAnimation = () => {
  return (
    <div className="absolute inset-0 w-full h-full pointer-events-none">
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes lineDraw {
          0% { 
            stroke-dashoffset: 1000; 
            opacity: 0.15;
          }
          40% { 
            stroke-dashoffset: 0; 
            opacity: 0.25;
          }
          60% { 
            stroke-dashoffset: 0; 
            opacity: 0.25;
          }
          100% { 
            stroke-dashoffset: -1000; 
            opacity: 0.15;
          }
        }
        
        .line-draw {
          animation: lineDraw 5s ease-in-out infinite;
          opacity: 0.15;
        }
        
        .line-draw-delayed {
          animation: lineDraw 5s ease-in-out 1.5s infinite;
          opacity: 0.15;
        }
        
        .line-draw-delayed-more {
          animation: lineDraw 5s ease-in-out 3s infinite;
          opacity: 0.15;
        }
        
        .revision-grid {
          position: absolute;
          inset: 0;
          width: 100%;
          height: 100%;
          background-image: linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                            linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
          background-size: 20px 20px;
          opacity: 0.15;
        }
      `}} />
      
      <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" preserveAspectRatio="none">
        {/* Horizontal lines */}
        <line 
          x1="0" y1="250" x2="1000" y2="250" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw"
        />
        <line 
          x1="0" y1="500" x2="1000" y2="500" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw-delayed"
        />
        <line 
          x1="0" y1="750" x2="1000" y2="750" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw-delayed-more"
        />
        
        {/* Vertical lines */}
        <line 
          x1="250" y1="0" x2="250" y2="1000" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw"
        />
        <line 
          x1="500" y1="0" x2="500" y2="1000" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw-delayed"
        />
        <line 
          x1="750" y1="0" x2="750" y2="1000" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw-delayed-more"
        />
        
        {/* Diagonal lines */}
        <line 
          x1="0" y1="0" x2="1000" y2="1000" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1414" 
          className="line-draw"
        />
        <line 
          x1="1000" y1="0" x2="0" y2="1000" 
          stroke="white" strokeWidth="0.5" strokeDasharray="1414" 
          className="line-draw-delayed"
        />
        
        {/* Curved lines representing revision/editing */}
        <path 
          d="M200,300 Q400,100 600,300 T1000,300" 
          fill="none" stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw"
        />
        <path 
          d="M0,700 Q200,900 400,700 T800,700" 
          fill="none" stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
          className="line-draw-delayed"
        />
      </svg>
      
      {/* Grid pattern using CSS instead of SVG */}
      <div className="revision-grid"></div>
    </div>
  );
};

// Random letter color change component for REVISION title (from homepage)
const RandomColorTitle = () => {
  return (
    <div className="title-container">
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes colorShiftBlue {
          0%, 100% {
            color: white;
            text-shadow: none;
          }
          10%, 15% {
            color: #60a5fa; /* Lighter blue for dark theme */
            text-shadow: 0 0 8px rgba(96, 165, 250, 0.7); /* Blue glow */
          }
        }
        
        .letter {
          display: inline-block;
        }
        
        /* Apply the same synchronized animation to R, E, V, S, N */
        .letter:nth-child(1),
        .letter:nth-child(2),
        .letter:nth-child(3),
        .letter:nth-child(5),
        .letter:nth-child(8) {
          animation: colorShiftBlue 5s infinite;
        }
        
        /* Mobile responsiveness fixes */
        @media (max-width: 640px) {
          .title-container h1 {
            font-size: 5rem;
            line-height: 1;
            letter-spacing: -0.05em;
            white-space: nowrap;
          }
        }
      `}} />
      
      <h1 className="text-8xl md:text-[9rem] font-bold tracking-tighter leading-[0.8] mb-6 text-white">
        <span className="letter">R</span>
        <span className="letter">E</span>
        <span className="letter">V</span>
        <span className="letter">I</span>
        <span className="letter">S</span>
        <span className="letter">I</span>
        <span className="letter">O</span>
        <span className="letter">N</span>
      </h1>
    </div>
  );
};

export default function AboutPage() {
  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />

      {/* Hero Section */}
      <section className="relative py-20 md:py-28 overflow-hidden">
        <LineDrawingAnimation />
        
        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <RandomColorTitle />
            
            <p className="text-2xl sm:text-3xl md:text-4xl font-extralight tracking-tight relative group">
              <span className="bg-gradient-to-r from-white to-white bg-[length:0%_1px] hover:bg-[length:100%_1px] bg-no-repeat bg-bottom transition-all duration-500">REVISED OUTLOOK</span>
            </p>
            
            <div className="h-[1px] w-20 bg-white mx-auto my-6 md:my-8 group-hover:w-40 transition-all duration-500"></div>
            
            <p className="text-2xl md:text-3xl font-medium tracking-tight mt-8 mb-16 text-gray-300">
              Clothing with purpose. A mindset in motion.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 md:py-24 relative overflow-hidden bg-neutral-900">
        <div className="container mx-auto px-4 md:px-8">
          <div className="max-w-3xl mx-auto">
            <p className="text-xl md:text-2xl leading-relaxed mb-12 text-gray-200">
              We&apos;re two brothers who started Revision with one goal: to make clothes we actually want to wear — stylish, comfortable, and ready for any scenario.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
              <div>
                <p className="text-lg leading-relaxed mb-6 text-gray-300">
                  For us, clothing isn&apos;t just about looking good. It&apos;s about feeling like yourself.
                  Revision is about expressing who you are and where you&apos;re going. Our pieces are made for everyday wear but designed to say something — about mindset, about growth, about confidence.
                </p>
              </div>
              
              <div>
                <p className="text-lg leading-relaxed mb-6 text-gray-300">
                  &quot;Revised Outlook&quot; isn&apos;t just a tagline — it&apos;s how we move.
                  It&apos;s setting goals, leveling up, and choosing to see things in a better light. No matter where you&apos;re at, you&apos;re always one decision away from shifting your path.
                </p>
              </div>
            </div>
            
            <div className="text-center mb-12">
              <p className="text-xl font-medium mb-2 text-white">
                This brand?
              </p>
              <p className="text-xl font-medium mb-2 text-white">
                It&apos;s more than fashion.
              </p>
              <p className="text-xl font-medium mb-12 text-white">
                It&apos;s a way of thinking.
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-2xl font-bold mb-2 text-white">
                Welcome to Revision.
              </p>
              <p className="text-2xl font-medium text-white">
                Let the fit speak for you.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Brothers Section */}
      <section className="py-16 md:py-24 bg-neutral-800">
        <div className="container mx-auto px-4 md:px-8">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tighter mb-12 text-center text-white">Founders</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            {/* Brother 1 */}
            <div className="text-center">
              <div className="w-56 h-56 mx-auto bg-neutral-700 mb-6 rounded-full"></div>
              <h3 className="text-xl font-medium mb-2 text-white">Paolo Pandino</h3>
              <div className="max-w-xs mx-auto">
                <Image 
                  src="/assets/pao-sig.png" 
                  alt="Paolo Pandino's signature" 
                  className="h-32 mx-auto"
                  width={300}
                  height={64}
                />
              </div>
            </div>
            
            {/* Brother 2 */}
            <div className="text-center">
              <div className="w-56 h-56 mx-auto bg-neutral-700 mb-6 rounded-full"></div>
              <h3 className="text-xl font-medium mb-2 text-white">Co-Founder</h3>
            </div>
          </div>
        </div>
      </section>
      
      {/* Mindset Section */}
      <section className="py-16 md:py-24 overflow-hidden relative bg-neutral-900">
        <div className="container mx-auto px-4 md:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tighter mb-8 text-white">Our Philosophy</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-16">
            <div className="p-6 border border-neutral-700 bg-neutral-800">
              <h3 className="text-xl font-medium mb-4 text-white">Purpose</h3>
              <p className="text-base leading-relaxed text-gray-300">
                Every design decision is intentional. We create clothing that serves a purpose in your life.
              </p>
            </div>
            
            <div className="p-6 border border-neutral-700 bg-neutral-800">
              <h3 className="text-xl font-medium mb-4 text-white">Mindset</h3>
              <p className="text-base leading-relaxed text-gray-300">
                Revision represents growth, adaptation, and the constant pursuit of better.
              </p>
            </div>
            
            <div className="p-6 border border-neutral-700 bg-neutral-800">
              <h3 className="text-xl font-medium mb-4 text-white">Motion</h3>
              <p className="text-base leading-relaxed text-gray-300">
                We design for movement — both physical and personal. Our clothes move with you through every phase.
              </p>
            </div>
          </div>
          
        </div>
      </section>

      <SiteFooter />
    </div>
  );
} 