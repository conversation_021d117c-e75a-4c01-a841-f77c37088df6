import { db } from '../src/db/index';
import { inventory, products, productVariants, colors, sizes } from '../src/db/schema';
import { eq } from 'drizzle-orm';

async function diagnoseInventoryVariants() {
  try {
    console.log('🔍 Diagnosing Cropped Zip Up Hoodie inventory variants...\n');

    // 1. Find the product
    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    if (!product || product.length === 0) {
      console.log('❌ Product "Cropped Zip Up Hoodie" not found');
      return;
    }

    console.log('✅ Found product:', {
      id: product[0].id,
      name: product[0].name,
      sku: product[0].sku,
      status: product[0].status
    });

    // 2. Get all colors and sizes
    const allColors = await db.select().from(colors);
    const allSizes = await db.select().from(sizes);

    console.log('\n📋 Available colors:');
    allColors.forEach(color => {
      console.log(`  - ${color.name} (${color.value}) [ID: ${color.id}]`);
    });

    console.log('\n📋 Available sizes:');
    allSizes.forEach(size => {
      console.log(`  - ${size.name} (${size.value}) [ID: ${size.id}]`);
    });

    // 3. Get all product variants for this product
    const variants = await db
      .select({
        variantId: productVariants.id,
        productId: productVariants.productId,
        colorId: productVariants.colorId,
        sizeId: productVariants.sizeId,
        colorName: colors.name,
        sizeName: sizes.name,
        sku: productVariants.sku,
        price: productVariants.price,
        createdAt: productVariants.createdAt
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(productVariants.productId, product[0].id));

    console.log(`\n🧩 Found ${variants.length} product variants:`);
    variants.forEach(variant => {
      console.log(`  - ${variant.colorName} ${variant.sizeName} [Variant ID: ${variant.variantId}]`);
      console.log(`    SKU: ${variant.sku}, Price: ${variant.price}`);
      console.log(`    Created: ${variant.createdAt}`);
    });

    // 4. Get all inventory records for this product
    const inventoryRecords = await db
      .select({
        inventoryId: inventory.id,
        productId: inventory.productId,
        variantId: inventory.variantId,
        quantity: inventory.quantity,
        updatedAt: inventory.updatedAt,
        colorName: colors.name,
        sizeName: sizes.name
      })
      .from(inventory)
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id));

    console.log(`\n📦 Found ${inventoryRecords.length} inventory records:`);
    inventoryRecords.forEach(record => {
      console.log(`  - ${record.colorName || 'NULL'} ${record.sizeName || 'NULL'}: ${record.quantity} units`);
      console.log(`    Inventory ID: ${record.inventoryId}`);
      console.log(`    Variant ID: ${record.variantId || 'NULL'}`);
      console.log(`    Updated: ${record.updatedAt}`);
    });

    // 5. Identify problematic variants
    console.log('\n🚨 Analyzing problematic variants:');
    
    const problematicVariants = [
      { color: 'Rustic Black', size: 'Small' },
      { color: 'Matcha Green', size: 'XL' },
      { color: 'Rustic Black', size: 'Large' }
    ];

    const workingVariants = [
      { color: 'Matcha Green', size: 'Small' },
      { color: 'Matcha Green', size: 'Medium' },
      { color: 'Matcha Green', size: 'Large' }
    ];

    console.log('\n❌ Problematic variants (should NOT work):');
    for (const variant of problematicVariants) {
      const variantRecord = variants.find(v => v.colorName === variant.color && v.sizeName === variant.size);
      const inventoryRecord = inventoryRecords.find(i => i.colorName === variant.color && i.sizeName === variant.size);
      
      console.log(`\n  ${variant.color} ${variant.size}:`);
      console.log(`    Variant exists: ${variantRecord ? 'YES' : 'NO'}`);
      if (variantRecord) {
        console.log(`    Variant ID: ${variantRecord.variantId}`);
        console.log(`    Created: ${variantRecord.createdAt}`);
      }
      console.log(`    Inventory exists: ${inventoryRecord ? 'YES' : 'NO'}`);
      if (inventoryRecord) {
        console.log(`    Inventory ID: ${inventoryRecord.inventoryId}`);
        console.log(`    Variant ID in inventory: ${inventoryRecord.variantId}`);
        console.log(`    Quantity: ${inventoryRecord.quantity}`);
        console.log(`    Updated: ${inventoryRecord.updatedAt}`);
      }
    }

    console.log('\n✅ Working variants (should work):');
    for (const variant of workingVariants) {
      const variantRecord = variants.find(v => v.colorName === variant.color && v.sizeName === variant.size);
      const inventoryRecord = inventoryRecords.find(i => i.colorName === variant.color && i.sizeName === variant.size);
      
      console.log(`\n  ${variant.color} ${variant.size}:`);
      console.log(`    Variant exists: ${variantRecord ? 'YES' : 'NO'}`);
      if (variantRecord) {
        console.log(`    Variant ID: ${variantRecord.variantId}`);
        console.log(`    Created: ${variantRecord.createdAt}`);
      }
      console.log(`    Inventory exists: ${inventoryRecord ? 'YES' : 'NO'}`);
      if (inventoryRecord) {
        console.log(`    Inventory ID: ${inventoryRecord.inventoryId}`);
        console.log(`    Variant ID in inventory: ${inventoryRecord.variantId}`);
        console.log(`    Quantity: ${inventoryRecord.quantity}`);
        console.log(`    Updated: ${inventoryRecord.updatedAt}`);
      }
    }

    // 6. Check for orphaned inventory records
    console.log('\n🔍 Checking for orphaned inventory records:');
    const orphanedInventory = inventoryRecords.filter(record => !record.variantId);
    if (orphanedInventory.length > 0) {
      console.log(`❌ Found ${orphanedInventory.length} orphaned inventory records (no variant ID):`);
      orphanedInventory.forEach(record => {
        console.log(`  - Inventory ID: ${record.inventoryId}, Quantity: ${record.quantity}`);
      });
    } else {
      console.log('✅ No orphaned inventory records found');
    }

    // 7. Check for missing inventory records
    console.log('\n🔍 Checking for variants without inventory:');
    const variantsWithoutInventory = variants.filter(variant => 
      !inventoryRecords.some(inv => inv.variantId === variant.variantId)
    );
    if (variantsWithoutInventory.length > 0) {
      console.log(`❌ Found ${variantsWithoutInventory.length} variants without inventory:`);
      variantsWithoutInventory.forEach(variant => {
        console.log(`  - ${variant.colorName} ${variant.sizeName} [Variant ID: ${variant.variantId}]`);
      });
    } else {
      console.log('✅ All variants have inventory records');
    }

    console.log('\n🎯 Diagnosis complete!');

  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
  }
}

// Run the diagnosis
diagnoseInventoryVariants().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Script error:', error);
  process.exit(1);
});
