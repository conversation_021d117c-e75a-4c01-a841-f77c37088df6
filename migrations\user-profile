  -- Check if profiles table exists already
  CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VA<PERSON>HAR(20),
    country VARCHAR(2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- If it exists, make sure it has all needed columns
  -- Add columns if they don't exist
  DO $$
  BEGIN
    -- Check and add first_name
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'first_name') THEN
      ALTER TABLE profiles ADD COLUMN first_name VARCHA<PERSON>(50);
    END IF;

    -- Check and add last_name
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'last_name') THEN
      ALTER TABLE profiles ADD COLUMN last_name VA<PERSON>HA<PERSON>(50);
    END IF;

    -- Check and add address_line1
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'address_line1') THEN
      ALTER TABLE profiles ADD COLUMN address_line1 VARCHAR(255);
    END IF;

    -- Check and add address_line2
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'address_line2') THEN
      ALTER TABLE profiles ADD COLUMN address_line2 VARCHAR(255);
    END IF;

    -- Check and add city
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'city') THEN
      ALTER TABLE profiles ADD COLUMN city VARCHAR(100);
    END IF;

    -- Check and add state
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'state') THEN
      ALTER TABLE profiles ADD COLUMN state VARCHAR(100);
    END IF;

    -- Check and add postal_code
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'postal_code') THEN
      ALTER TABLE profiles ADD COLUMN postal_code VARCHAR(20);
    END IF;

    -- Check and add country
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'country') THEN
      ALTER TABLE profiles ADD COLUMN country VARCHAR(2);
    END IF;

    -- Check and add created_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'created_at') THEN
      ALTER TABLE profiles ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- Check and add updated_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'updated_at') THEN
      ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
  END $$;

  -- Set up Row Level Security
  ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

  -- Create policies if they don't exist
  DO $$
  BEGIN
    -- Check if the select policy exists
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can view their
   own profile') THEN
      CREATE POLICY "Users can view their own profile"
        ON profiles FOR SELECT
        USING (auth.uid() = id);
    END IF;

    -- Check if the update policy exists
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can update
  their own profile') THEN
      CREATE POLICY "Users can update their own profile"
        ON profiles FOR UPDATE
        USING (auth.uid() = id);
    END IF;

    -- Check if the insert policy exists
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can insert
  their own profile') THEN
      CREATE POLICY "Users can insert their own profile"
        ON profiles FOR INSERT
        WITH CHECK (auth.uid() = id);
    END IF;
  END $$;

  -- Create a function to handle new user creation
  CREATE OR REPLACE FUNCTION public.handle_new_user()
  RETURNS TRIGGER AS $$
  BEGIN
    INSERT INTO public.profiles (id)
    VALUES (NEW.id);
    RETURN NEW;
  END;
  $$ LANGUAGE plpgsql SECURITY DEFINER;

  -- Create a trigger that runs the function on user creation
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();