import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export default async function DashboardPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    // If no user is found, redirect to login
    redirect('/login')
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      <div className="bg-neutral-800 rounded-lg p-6">
        <h2 className="text-xl font-medium mb-4">Welcome, {data.user.email}</h2>
        <p className="text-gray-300 mb-2">User ID: {data.user.id}</p>
        <p className="text-gray-300 mb-4">Last Sign In: {new Date(data.user.last_sign_in_at || '').toLocaleString()}</p>
        
        <h3 className="text-lg font-medium mt-6 mb-2">Account Information</h3>
        <p className="text-gray-300">This is a protected route that only authenticated users can access.</p>
      </div>
    </div>
  )
}