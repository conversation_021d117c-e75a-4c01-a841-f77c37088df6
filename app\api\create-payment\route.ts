import { SquareClient, SquareEnvironment, SquareError } from "square";
import { Square } from "square";
import { NextResponse } from "next/server";
import { randomUUID } from "crypto";
import { createOrder } from "@/src/db/orders";
import { createClient } from "@/utils/supabase/server";

// Initialize Square client
const client = new SquareClient({ 
  token: process.env.SQUARE_ACCESS_TOKEN,
  environment: process.env.SQUARE_ENVIRONMENT === "production" 
    ? SquareEnvironment.Production
    : SquareEnvironment.Sandbox,
});

export async function POST(req: Request) {
  try {
    const { sourceId, amount, orderData } = await req.json();

    if (!sourceId || !amount || !orderData) {
      return NextResponse.json(
        { error: "Missing required fields: sourceId, amount, and orderData" },
        { status: 400 }
      );
    }
    
    // Basic validation for amount (you might want more robust validation)
    if (typeof amount !== 'number' || amount <= 0) {
        return NextResponse.json(
          { error: "Invalid amount" },
          { status: 400 }
        );
    }

    const payment = {
      sourceId: sourceId,
      idempotencyKey: randomUUID(), // Ensures the request is processed only once
      amountMoney: {
        // Amount in the smallest currency unit (e.g., cents for USD)
        // Make sure to convert your dollar amount to cents
        amount: BigInt(Math.round(amount * 100)), 
        currency: "USD" as Square.Currency, // Use the proper Square namespace
      },
      // Add other details like orderId, customerId, note, etc. if needed
      // locationId: process.env.SQUARE_LOCATION_ID, // Optional: If omitted, uses the default location for the access token.
    };

    const response = await client.payments.create(payment);

    if (response.payment && response.payment.status === 'COMPLETED') {
        // console.log("Payment successful:", response.payment); // Removed detailed log

        // Get current user
        const supabase = await createClient();
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          return NextResponse.json(
            { error: "User not authenticated" },
            { status: 401 }
          );
        }

        // Create order in database
        const order = await createOrder({
          userId: user.id,
          email: orderData.email,
          subtotal: orderData.subtotal,
          shipping: orderData.shipping,
          tax: orderData.tax,
          total: orderData.total,
          shippingAddress: orderData.shippingAddress,
          billingAddress: orderData.billingAddress,
          paymentIntentId: response.payment.id,
          items: orderData.items,
        });

        // Create a JSON-serializable version of the payment details
        const serializablePayment = {
            id: response.payment.id,
            status: response.payment.status,
            amount: response.payment.amountMoney?.amount?.toString(), // Convert BigInt to string
            currency: response.payment.amountMoney?.currency,
            orderId: order.id,
            receiptUrl: response.payment.receiptUrl,
            cardDetails: {
                cardBrand: response.payment.cardDetails?.card?.cardBrand,
                last4: response.payment.cardDetails?.card?.last4,
            }
            // Add any other relevant fields, converting BigInts as needed
        };

        return NextResponse.json({ success: true, payment: serializablePayment, orderId: order.id });
    } else {
        // Keep this error log for server-side visibility of failures
        console.error("Square Payment Failed or Status Not Completed:", { 
            status: response.payment?.status, 
            errors: response.errors 
        });
        return NextResponse.json(
          { 
            success: false, 
            error: "Payment failed", 
            details: response.errors || `Status: ${response.payment?.status}` 
          },
          { status: 400 } 
        );
    }

  } catch (error) {
    // Keep this general catch block error log
    console.error("Error in /api/create-payment:", error);
    let errorMessage = "Internal server error";
    if (error instanceof SquareError) { 
        errorMessage = error.errors?.map(e => e.detail).join(', ') || error.message;
        return NextResponse.json(
            { error: "Failed to process payment with Square", details: errorMessage },
            { status: error.statusCode || 500 } 
        );
    } else if (error instanceof Error) {
        errorMessage = error.message;
    }
    return NextResponse.json(
      { error: "Failed to process payment", details: errorMessage },
      { status: 500 }
    );
  }
} 