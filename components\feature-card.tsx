import { ReactNode } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button, buttonVariants } from "@/components/ui/button";
import { VariantProps } from "class-variance-authority";

interface FeatureCardProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
  footerContent?: ReactNode;
  buttonText?: string;
  buttonVariant?: VariantProps<typeof buttonVariants>["variant"];
  buttonHref?: string;
  buttonOnClick?: () => void;
}

export function FeatureCard({
  title,
  description,
  icon,
  children,
  footerContent,
  buttonText,
  buttonVariant = "default",
  buttonHref,
  buttonOnClick,
}: FeatureCardProps) {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        {icon && <div className="mb-2">{icon}</div>}
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="flex-grow">{children}</CardContent>
      {(footerContent || buttonText) && (
        <CardFooter className="flex justify-between items-center">
          {footerContent}
          {buttonText && (
            buttonHref ? (
              <Button variant={buttonVariant} asChild>
                <a href={buttonHref} target={buttonHref.startsWith("http") ? "_blank" : undefined} rel={buttonHref.startsWith("http") ? "noopener noreferrer" : undefined}>
                  {buttonText}
                </a>
              </Button>
            ) : (
              <Button variant={buttonVariant} onClick={buttonOnClick}>
                {buttonText}
              </Button>
            )
          )}
        </CardFooter>
      )}
    </Card>
  );
} 