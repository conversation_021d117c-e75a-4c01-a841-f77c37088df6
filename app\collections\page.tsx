"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";

// Collection category type
type CollectionCategory = {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  badge?: string;
  slug: string;
};

// Collection categories data
const collectionCategories: CollectionCategory[] = [
  {
    id: "1",
    title: "Cropped Hoodie",
    description: "Our signature cropped hoodie in three distinct colorways - Rustic Black, Sky Blue, and Matcha Green - featuring unique details and premium materials.",
    imageUrl: "/assets/black-hood.JPG",
    badge: "Featured",
    slug: "cropped-hoodie"
  },
  {
    id: "2",
    title: "More Coming Soon...",
    description: "We're working on expanding our collection. Stay tuned for new designs and products in the near future.",
    imageUrl: "/assets/bold-logo.JPEG",
    badge: "Coming Soon",
    slug: "#"
  }
];

export default function CollectionsPage() {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4 md:px-6">
          {/* Page Header */}
          <div className="mb-16 text-center">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-4">Collections</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Explore our carefully curated collections, each representing a different facet of the Revision aesthetic.
            </p>
          </div>
          
          {/* Featured Collection */}
          <div className="mb-20">
            <div className="relative h-[60vh] md:h-[70vh] overflow-hidden group">
              <Link href="/collections/cropped-hoodie">
                <Image
                  src="/assets/all-top.JPG"
                  alt="Featured collection of cropped hoodies"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                  sizes="100vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80"></div>
                <div className="absolute bottom-0 left-0 p-6 md:p-12 w-full md:w-2/3">
                  <div className="inline-block px-3 py-1 bg-white text-black text-sm font-medium mb-4">
                    Featured Collection
                  </div>
                  <h2 className="text-3xl md:text-5xl font-bold mb-4">Cropped Hoodie</h2>
                  <p className="text-lg md:text-xl text-gray-200 mb-6 max-w-xl">
                    Our signature piece, available in three distinct colorways. Each hoodie is crafted with attention to detail, from the distinctive sleeve prints to the carefully selected fabrics.
                  </p>
                  <Button className="rounded-none bg-white text-black hover:bg-gray-200">
                    View Collection
                  </Button>
                </div>
              </Link>
            </div>
          </div>
          
          {/* Collection Categories */}
          <div className="mb-16">
            <h2 className="text-2xl md:text-3xl font-bold mb-8">Browse All Collections</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {collectionCategories.map((category) => (
                <div
                  key={category.id}
                  className="relative overflow-hidden border border-neutral-700 bg-neutral-800"
                  onMouseEnter={() => setHoveredCategory(category.id)}
                  onMouseLeave={() => setHoveredCategory(null)}
                >
                  {category.slug !== "#" ? (
                    <Link href={`/collections/${category.slug}`}>
                      <div className="aspect-[4/5] relative">
                        <Image
                          src={category.imageUrl}
                          alt={category.title}
                          fill
                          className={`object-cover transition-transform duration-500 ${
                            hoveredCategory === category.id ? 'scale-105' : 'scale-100'
                          } ${category.imageUrl.includes('-back.JPG') ? 'rotate-[-90deg] scale-[0.85] origin-center' : ''}`}
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                        <div 
                          className={`absolute inset-0 bg-gradient-to-t from-black to-transparent transition-opacity duration-500 ${
                            hoveredCategory === category.id ? 'opacity-80' : 'opacity-60'
                          }`}
                        ></div>
                        
                        {category.badge && (
                          <div className="absolute top-4 right-4 px-3 py-1 bg-white text-black text-sm font-medium">
                            {category.badge}
                          </div>
                        )}
                      </div>
                      
                      <div className="absolute bottom-0 left-0 w-full p-6">
                        <h3 className="text-2xl font-bold mb-2">{category.title}</h3>
                        <p className="text-gray-300 mb-4 line-clamp-2 md:line-clamp-3">{category.description}</p>
                        <div 
                          className={`inline-flex items-center transition-all duration-300 ${
                            hoveredCategory === category.id ? 'translate-x-2' : 'translate-x-0'
                          }`}
                        >
                          <span className="mr-2">Explore</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                          </svg>
                        </div>
                      </div>
                    </Link>
                  ) : (
                    <div>
                      <div className="aspect-[4/5] relative flex items-center justify-center">
                        <Image
                          src={category.imageUrl}
                          alt={category.title}
                          fill
                          className="object-contain p-8 opacity-50"
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
                        
                        {category.badge && (
                          <div className="absolute top-4 right-4 px-3 py-1 bg-white text-black text-sm font-medium">
                            {category.badge}
                          </div>
                        )}
                      </div>
                      
                      <div className="absolute bottom-0 left-0 w-full p-6">
                        <h3 className="text-2xl font-bold mb-2">{category.title}</h3>
                        <p className="text-gray-300 mb-4">{category.description}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
        </div>
      </main>
      
      <SiteFooter />
    </div>
  );
}