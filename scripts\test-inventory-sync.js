#!/usr/bin/env node

/**
 * Test script to verify inventory synchronization
 * This script helps debug inventory sync issues between admin updates and frontend display
 */

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

async function testInventorySync() {
  console.log('🧪 Testing Inventory Synchronization...\n');

  try {
    // Test 1: Fetch current inventory
    console.log('📋 Test 1: Fetching current inventory...');
    const inventoryResponse = await fetch(`${BASE_URL}/api/products/inventory?productName=Cropped Zip Up Hoodie&t=${Date.now()}`);
    
    if (!inventoryResponse.ok) {
      throw new Error(`Inventory fetch failed: ${inventoryResponse.status}`);
    }
    
    const inventoryData = await inventoryResponse.json();
    console.log('✅ Current inventory:', JSON.stringify(inventoryData.inventory, null, 2));
    console.log('📅 Last updated:', inventoryData.lastUpdated);
    
    // Test 2: Check cache headers
    console.log('\n🔍 Test 2: Checking cache headers...');
    const cacheControl = inventoryResponse.headers.get('cache-control');
    const pragma = inventoryResponse.headers.get('pragma');
    const expires = inventoryResponse.headers.get('expires');
    
    console.log('Cache-Control:', cacheControl);
    console.log('Pragma:', pragma);
    console.log('Expires:', expires);
    
    if (cacheControl?.includes('no-cache') && pragma === 'no-cache' && expires === '0') {
      console.log('✅ Cache headers are correctly set for no caching');
    } else {
      console.log('⚠️  Cache headers may allow caching');
    }
    
    // Test 3: Test with refresh parameter
    console.log('\n🔄 Test 3: Testing with refresh parameter...');
    const refreshResponse = await fetch(`${BASE_URL}/api/products/inventory?productName=Cropped Zip Up Hoodie&refresh=true&t=${Date.now()}`);
    
    if (!refreshResponse.ok) {
      throw new Error(`Refresh fetch failed: ${refreshResponse.status}`);
    }
    
    const refreshData = await refreshResponse.json();
    console.log('✅ Refresh fetch successful');
    console.log('📅 Refresh last updated:', refreshData.lastUpdated);
    
    // Test 4: Compare timestamps
    console.log('\n⏰ Test 4: Comparing timestamps...');
    const timeDiff = new Date(refreshData.lastUpdated) - new Date(inventoryData.lastUpdated);
    console.log(`Time difference: ${timeDiff}ms`);
    
    if (timeDiff >= 0) {
      console.log('✅ Refresh returned newer or same data');
    } else {
      console.log('⚠️  Refresh returned older data');
    }
    
    // Test 5: Check for specific inventory items
    console.log('\n📦 Test 5: Checking specific inventory items...');
    const inventory = inventoryData.inventory;
    
    if (inventory['Rustic Black']) {
      console.log('✅ Rustic Black variants found:', inventory['Rustic Black']);
    } else {
      console.log('❌ Rustic Black variants not found');
    }
    
    if (inventory['Sky Blue']) {
      console.log('✅ Sky Blue variants found:', inventory['Sky Blue']);
    } else {
      console.log('❌ Sky Blue variants not found');
    }
    
    if (inventory['Matcha Green']) {
      console.log('✅ Matcha Green variants found:', inventory['Matcha Green']);
    } else {
      console.log('❌ Matcha Green variants not found');
    }
    
    console.log('\n🎉 Inventory synchronization test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testInventorySync();
