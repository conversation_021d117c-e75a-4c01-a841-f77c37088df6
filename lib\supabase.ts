// Import the client creation function
import { createClient as createBrowserClient } from '@/utils/supabase/client'
import { createClient as createServerClient } from '@/utils/supabase/server'
import { updateSession } from '@/utils/supabase/middleware'

// Re-export the client creation functions for flexibility
export { createBrowserClient as createClientBrowser, createServerClient as createClientServer }
export { updateSession }

// Create a function to get a fresh client instance instead of a singleton
export function getSupabaseClient() {
  return createBrowserClient();
}

// Default export for convenience - use function instead of singleton
export default getSupabaseClient;
