export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  variantId?: string | null;
  name: string;
  description?: string | null;
  price: string;
  quantity: number;
  subtotal: string;
  color?: string;
  size?: string;
  imageSrc?: string;
  imageAlt?: string;
}

export interface Order {
  id: string;
  userId: string;
  email: string;
  total: string;
  status: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  paymentIntentId?: string | null;
  discountTotal: string;
  shippingTotal: string;
  subtotal: string;
  createdAt: string;
  updatedAt: string;
  items: OrderItem[];
}

export interface TransformedOrder {
  orderId: string;
  orderDate: string;
  items: Array<{
    id: string;
    name: string;
    price: string;
    quantity: number;
    description?: string;
    total: number;
    color?: string;
    size?: string;
    imageSrc?: string;
    imageAlt?: string;
  }>;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  payment: {
    status: string;
    paymentIntentId?: string;
  };
  customer: {
    email: string;
  };
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
}