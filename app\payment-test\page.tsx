"use client";

import { useState, useCallback } from "react";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";
import SquarePaymentForm from "@/components/square-payment-form";

// Retrieve Square credentials from environment variables
const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;

export default function PaymentTestPage() {
  const [amount, setAmount] = useState(10.00);
  const [paymentResult, setPaymentResult] = useState<null | {
    success: boolean;
    message: string;
    details?: Record<string, unknown>;
  }>(null);
  const [isSquareReady, setIsSquareReady] = useState(false);

  // Check if Square is properly configured
  useState(() => {
    if (squareApplicationId && squareLocationId) {
      setIsSquareReady(true);
    }
  });

  const handlePaymentSuccess = useCallback((paymentDetails: Record<string, unknown>) => {
    setPaymentResult({
      success: true,
      message: "Payment processed successfully!",
      details: paymentDetails
    });
  }, []);

  const handlePaymentFailure = useCallback((error: unknown) => {
    let message = "An unexpected error occurred during payment.";
    if (error instanceof Error) {
      message = error.message;
    }
    setPaymentResult({
      success: false,
      message
    });
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      <main className="flex-1 py-10 px-4 sm:px-6 md:px-8 lg:px-16 max-w-7xl mx-auto w-full">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-4xl font-bold tracking-tight mb-6">Payment Gateway Test</h1>
          
          <div className="bg-neutral-800 p-6 mb-8 border border-neutral-700">
            <h2 className="text-xl font-bold mb-4">Test Configuration</h2>
            <div className="mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-neutral-400 mb-1">Application ID:</p>
                  <p className="font-mono text-sm bg-neutral-900 p-2 rounded">
                    {squareApplicationId ? 
                      `${squareApplicationId.substring(0, 6)}...` : 
                      "Not configured"
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm text-neutral-400 mb-1">Location ID:</p>
                  <p className="font-mono text-sm bg-neutral-900 p-2 rounded">
                    {squareLocationId ? 
                      `${squareLocationId.substring(0, 6)}...` : 
                      "Not configured"
                    }
                  </p>
                </div>
              </div>
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">
                Test Payment Amount ($):
              </label>
              <div className="grid grid-cols-4 gap-2">
                {[5, 10, 25, 50].map((val) => (
                  <Button 
                    key={val}
                    onClick={() => setAmount(val)}
                    variant={amount === val ? "default" : "outline"}
                    className={`rounded-none ${amount === val ? 'bg-white text-black' : 'border-white text-white'}`}
                  >
                    ${val.toFixed(2)}
                  </Button>
                ))}
              </div>
            </div>

            <div className="pt-4 border-t border-neutral-700">
              <h3 className="text-lg font-medium mb-4">Test Card Information</h3>
              <p className="text-sm text-neutral-400 mb-2">
                Use these test cards for sandbox testing:
              </p>
              <div className="bg-neutral-900 p-4 rounded-md font-mono text-xs mb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                  <div className="p-2">
                    <span className="text-neutral-500 block">Card Number:</span>
                    <span className="text-white">4111 1111 1111 1111</span>
                  </div>
                  <div className="p-2">
                    <span className="text-neutral-500 block">CVV:</span>
                    <span className="text-white">Any 3 digits</span>
                  </div>
                  <div className="p-2">
                    <span className="text-neutral-500 block">Expiration:</span>
                    <span className="text-white">Any future date</span>
                  </div>
                  <div className="p-2">
                    <span className="text-neutral-500 block">Postal Code:</span>
                    <span className="text-white">Any 5 digits</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {paymentResult && (
            <div className={`mb-8 p-6 border rounded-md ${
              paymentResult.success 
                ? 'border-green-600 bg-green-900/20 text-green-200' 
                : 'border-red-600 bg-red-900/20 text-red-200'
            }`}>
              <h3 className="text-lg font-bold mb-2">
                {paymentResult.success ? 'Payment Successful' : 'Payment Failed'}
              </h3>
              <p className="mb-4">{paymentResult.message}</p>
              
              {paymentResult.success && paymentResult.details && (
                <div className="bg-neutral-900/50 p-4 rounded-md">
                  <p className="text-sm mb-2 text-neutral-400">Transaction Details:</p>
                  <pre className="text-xs overflow-auto">
                    {JSON.stringify(paymentResult.details, null, 2)}
                  </pre>
                </div>
              )}
              
              <Button 
                onClick={() => setPaymentResult(null)} 
                variant="outline"
                className="mt-4 text-sm border-current"
              >
                Clear Results
              </Button>
            </div>
          )}

          <div className="bg-neutral-800 p-6 border border-neutral-700">
            <h2 className="text-xl font-bold mb-6 text-white">Payment Form</h2>
            
            {isSquareReady ? (
              <SquarePaymentForm
                applicationId={squareApplicationId!}
                locationId={squareLocationId!}
                amount={amount}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentFailure={handlePaymentFailure}
              />
            ) : (
              <div className="text-center text-neutral-400 text-sm p-8 border border-dashed border-neutral-600">
                <p className="mb-4">Payment system is not configured.</p>
                <p>Check your <code className="bg-neutral-900 px-2 py-1 rounded">.env.local</code> file for Square credentials.</p>
              </div>
            )}
          </div>
        </div>
      </main>
      
      <SiteFooter />
    </div>
  );
}