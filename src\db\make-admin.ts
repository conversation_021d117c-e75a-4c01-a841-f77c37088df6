import { db } from './index';
import { profiles } from './schema';
import { eq } from 'drizzle-orm';

// This function is kept for potential future use with email-based admin creation
// async function makeUserAdmin(email: string) {
//   try {
//     console.log(`Making user with email ${email} an admin...`);
//     
//     // First, get the user ID from Supabase auth
//     const { createClient } = await import('@/utils/supabase/server');
//     const supabase = await createClient();
//     
//     // Get user by email
//     const { data: { users }, error } = await supabase.auth.admin.listUsers();
//     
//     if (error) {
//       console.error('Error fetching users:', error);
//       // Fallback: try to update by email pattern in profiles
//       console.log('Attempting direct profile update...');
//       
//       // Since we can't get the user ID, we'll need to update the profile another way
//       // For now, let's create a simpler script that takes the user ID directly
//       throw new Error('Please provide the user ID directly');
//     }
//     
//     const user = users.find(u => u.email === email);
//     
//     if (!user) {
//       console.error(`User with email ${email} not found`);
//       process.exit(1);
//     }
//     
//     // Update the profile to set isAdmin = true
//     await db
//       .update(profiles)
//       .set({ 
//         isAdmin: true,
//         updatedAt: new Date().toISOString()
//       })
//       .where(eq(profiles.id, user.id));
//     
//     console.log(`Successfully made user ${email} an admin!`);
//     process.exit(0);
//   } catch (error) {
//     console.error('Error making user admin:', error);
//     process.exit(1);
//   }
// }

// Simple version that takes user ID directly
async function makeUserAdminById(userId: string) {
  try {
    console.log(`Making user with ID ${userId} an admin...`);
    
    // Check if profile exists
    const [existingProfile] = await db
      .select()
      .from(profiles)
      .where(eq(profiles.id, userId))
      .limit(1);
    
    if (!existingProfile) {
      // Create profile if it doesn't exist
      console.log('Profile does not exist, creating...');
      await db.insert(profiles).values({
        id: userId,
        isAdmin: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    } else {
      // Update existing profile
      await db
        .update(profiles)
        .set({ 
          isAdmin: true,
          updatedAt: new Date().toISOString()
        })
        .where(eq(profiles.id, userId));
    }
    
    console.log(`Successfully made user ${userId} an admin!`);
    process.exit(0);
  } catch (error) {
    console.error('Error making user admin:', error);
    process.exit(1);
  }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('Usage:');
  console.log('  npm run make-admin <user-id>');
  console.log('');
  console.log('To find your user ID:');
  console.log('  1. Go to your Supabase dashboard');
  console.log('  2. Navigate to Authentication > Users');
  console.log('  3. Find your user and copy the ID');
  console.log('');
  console.log('Or check the browser console when logged in - the user ID is often logged there.');
  process.exit(1);
}

const userId = args[0];
makeUserAdminById(userId);