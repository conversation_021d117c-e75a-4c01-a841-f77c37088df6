"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";

// Collection item type
type CollectionItem = {
  id: string;
  title: string;
  imageUrl: string;
  alt: string;
};

// Sample collection data focusing on Cropped Hoodie
const collectionsData: CollectionItem[] = [
  {
    id: "1",
    title: "Cropped Hoodie - Black Front",
    imageUrl: "/assets/black-hood.JPG",
    alt: "Black cropped hoodie front view"
  },
  {
    id: "2",
    title: "Cropped Hoodie - Black Back",
    imageUrl: "/assets/black-back.JPG",
    alt: "Black cropped hoodie back view"
  },
  {
    id: "3",
    title: "Cropped Hoodie - Black No Hood",
    imageUrl: "/assets/black-no-hood.JPG",
    alt: "Black cropped hoodie without hood"
  },
  {
    id: "4",
    title: "Cropped Hoodie - Blue Front",
    imageUrl: "/assets/blue-hood.JPG",
    alt: "Blue cropped hoodie front view"
  },
  {
    id: "5",
    title: "Cropped Hoodie - Blue Back",
    imageUrl: "/assets/blue-back.JPG",
    alt: "Blue cropped hoodie back view"
  },
  {
    id: "6",
    title: "Cropped Hoodie - Blue No Hood",
    imageUrl: "/assets/blue-no-hood.JPG",
    alt: "Blue cropped hoodie without hood"
  },
  {
    id: "7",
    title: "Cropped Hoodie - Green Front",
    imageUrl: "/assets/green-hood.JPG",
    alt: "Green cropped hoodie front view"
  },
  {
    id: "8",
    title: "Cropped Hoodie - Green Back",
    imageUrl: "/assets/green-back.JPG",
    alt: "Green cropped hoodie back view"
  },
  {
    id: "9",
    title: "Cropped Hoodie - Green No Hood",
    imageUrl: "/assets/green-no-hood.JPG",
    alt: "Green cropped hoodie without hood"
  },
  {
    id: "10",
    title: "All Colors - Top View",
    imageUrl: "/assets/all-top.JPG",
    alt: "All cropped hoodie colors top view"
  },
  {
    id: "11",
    title: "Detail - Logo Print",
    imageUrl: "/assets/all-prints.JPG",
    alt: "Detail of logo print on hoodie"
  },
  {
    id: "12",
    title: "Detail - Left Sleeve",
    imageUrl: "/assets/all-bot-left.JPG",
    alt: "Detail of left sleeve print"
  },
  {
    id: "13",
    title: "Detail - Right Sleeve",
    imageUrl: "/assets/all-bot-right.JPG",
    alt: "Detail of right sleeve print"
  },
  {
    id: "14",
    title: "Detail - Arm Print",
    imageUrl: "/assets/arm-print.JPG",
    alt: "Detail of arm print on hoodie"
  },
];

export default function CroppedHoodiePage() {
  const [selectedImage, setSelectedImage] = useState<CollectionItem | null>(null);
  const [layout, setLayout] = useState<'grid' | 'masonry' | 'rows'>('masonry');
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  
  // Handle clicking outside the lightbox to close it
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isLightboxOpen) {
        setIsLightboxOpen(false);
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isLightboxOpen]);

  // Handle image click to open lightbox
  const handleImageClick = (item: CollectionItem) => {
    setSelectedImage(item);
    setIsLightboxOpen(true);
  };

  // Close the lightbox
  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  // Get layout class based on current layout selection
  const getLayoutClass = () => {
    switch (layout) {
      case 'grid':
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4";
      case 'masonry':
        return "columns-1 sm:columns-2 lg:columns-3 gap-4 space-y-4";
      case 'rows':
        return "flex flex-col gap-4";
      default:
        return "columns-1 sm:columns-2 lg:columns-3 gap-4 space-y-4";
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4 md:px-6">
          {/* Breadcrumb */}
          <div className="mb-6">
            <nav className="flex text-sm">
              <Link href="/" className="text-gray-400 hover:text-white">Home</Link>
              <span className="mx-2 text-gray-500">/</span>
              <Link href="/collections" className="text-gray-400 hover:text-white">Collections</Link>
              <span className="mx-2 text-gray-500">/</span>
              <span className="text-white">Cropped Hoodie</span>
            </nav>
          </div>

          {/* Page Header */}
          <div className="mb-12 text-center">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-4">Cropped Hoodie Collection</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-4">
              A showcase of our signature cropped hoodie in various colors and angles.
            </p>
            <div className="max-w-3xl mx-auto">
              <p className="text-gray-300 mb-4 leading-relaxed">
                Our signature cropped hoodie is designed for versatility and style. Available in three distinct colorways - Rustic Black, Sky Blue, and Matcha Green - each piece features unique details and premium materials that set it apart.
              </p>
              <p className="text-gray-300 leading-relaxed">
                Every hoodie is crafted with attention to detail, from the distinctive sleeve prints to the carefully selected fabrics. The perfect blend of comfort and contemporary design, made for everyday wear with a distinctive edge.
              </p>
            </div>
          </div>
          
          {/* Layout Controls */}
          <div className="flex justify-center mb-8">
            <div className="inline-flex border border-neutral-700 rounded-md overflow-hidden">
              <button 
                onClick={() => setLayout('grid')} 
                className={`px-4 py-2 text-sm ${layout === 'grid' ? 'bg-white text-black' : 'text-gray-300'}`}
                aria-label="Grid layout"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="inline-block mr-1">
                  <rect x="3" y="3" width="7" height="7" />
                  <rect x="14" y="3" width="7" height="7" />
                  <rect x="14" y="14" width="7" height="7" />
                  <rect x="3" y="14" width="7" height="7" />
                </svg>
                Grid
              </button>
              <button 
                onClick={() => setLayout('masonry')} 
                className={`px-4 py-2 text-sm ${layout === 'masonry' ? 'bg-white text-black' : 'text-gray-300'}`}
                aria-label="Masonry layout"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="inline-block mr-1">
                  <rect x="3" y="3" width="18" height="6" />
                  <rect x="3" y="15" width="18" height="6" />
                  <rect x="12" y="9" width="9" height="6" />
                  <rect x="3" y="9" width="9" height="6" />
                </svg>
                Masonry
              </button>
              <button 
                onClick={() => setLayout('rows')} 
                className={`px-4 py-2 text-sm ${layout === 'rows' ? 'bg-white text-black' : 'text-gray-300'}`}
                aria-label="Rows layout"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="inline-block mr-1">
                  <line x1="3" y1="6" x2="21" y2="6" />
                  <line x1="3" y1="12" x2="21" y2="12" />
                  <line x1="3" y1="18" x2="21" y2="18" />
                </svg>
                Rows
              </button>
            </div>
          </div>
          
          {/* Gallery */}
          <div className={getLayoutClass()}>
            {collectionsData.map((item) => (
              layout === 'masonry' ? (
                <div 
                  key={item.id}
                  className="break-inside-avoid mb-4"
                  onClick={() => handleImageClick(item)}
                >
                  <div className="group relative overflow-hidden bg-neutral-800 border border-neutral-700 cursor-pointer transition-transform duration-300 hover:scale-[1.02]">
                    <div className="aspect-auto relative">
                      <Image
                        src={item.imageUrl}
                        alt={item.alt}
                        width={800}
                        height={800}
                        className={`w-full h-auto object-cover ${item.imageUrl.includes('-back.JPG') ? 'rotate-[-90deg] scale-[0.85] origin-center' : ''}`}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <div className="p-2 bg-black/60 rounded-full">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            <line x1="11" y1="8" x2="11" y2="14"></line>
                            <line x1="8" y1="11" x2="14" y2="11"></line>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : layout === 'rows' ? (
                <div 
                  key={item.id}
                  className="w-full"
                  onClick={() => handleImageClick(item)}
                >
                  <div className="group relative overflow-hidden bg-neutral-800 border border-neutral-700 cursor-pointer">
                    <div className="aspect-auto md:flex md:items-center">
                      <div className="md:w-2/3 relative">
                        <Image
                          src={item.imageUrl}
                          alt={item.alt}
                          width={800}
                          height={800}
                          className={`w-full h-auto object-contain ${item.imageUrl.includes('-back.JPG') ? 'rotate-[-90deg] scale-[0.85] origin-center' : ''}`}
                          sizes="(max-width: 768px) 100vw, 66vw"
                        />
                      </div>
                      <div className="p-4 md:w-1/3">
                        <h3 className="text-xl font-medium mb-2">{item.title}</h3>
                        <p className="text-gray-400 text-sm">{item.alt}</p>
                        <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button variant="outline" className="rounded-none border-white/70 bg-transparent text-white hover:bg-white hover:text-black">
                            View Larger
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div 
                  key={item.id}
                  onClick={() => handleImageClick(item)}
                  className="group relative overflow-hidden bg-neutral-800 border border-neutral-700 cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
                >
                  <div className="aspect-square relative">
                    <Image
                      src={item.imageUrl}
                      alt={item.alt}
                      fill
                      className={`object-cover ${item.imageUrl.includes('-back.JPG') ? 'rotate-[-90deg] scale-[0.85] origin-center' : ''}`}
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <div className="p-3 bg-black/60 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="m15 3 6 6m0 0-6 6m6-6H3"></path>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-medium truncate">{item.title}</h3>
                  </div>
                </div>
              )
            ))}
          </div>

          {/* Call to action */}
          <div className="mt-16 text-center">
            <Link href="/products/cropped-hoodie">
              <Button className="rounded-none bg-white text-black hover:bg-gray-200">
                Shop Cropped Hoodie
              </Button>
            </Link>
          </div>
        </div>
      </main>
      
      {/* Lightbox */}
      {isLightboxOpen && selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 md:p-8"
          onClick={closeLightbox}
        >
          {/* Close button */}
          <button 
            className="absolute top-4 right-4 p-2 text-white/80 hover:text-white bg-black/50 rounded-full z-10"
            onClick={closeLightbox}
            aria-label="Close lightbox"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          
          {/* Navigation buttons */}
          <button 
            className="absolute left-4 top-1/2 -translate-y-1/2 p-2 text-white/80 hover:text-white bg-black/50 rounded-full z-10"
            onClick={(e) => {
              e.stopPropagation();
              const currentIndex = collectionsData.findIndex(item => item.id === selectedImage.id);
              const prevIndex = (currentIndex - 1 + collectionsData.length) % collectionsData.length;
              setSelectedImage(collectionsData[prevIndex]);
            }}
            aria-label="Previous image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          
          <button 
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 text-white/80 hover:text-white bg-black/50 rounded-full z-10"
            onClick={(e) => {
              e.stopPropagation();
              const currentIndex = collectionsData.findIndex(item => item.id === selectedImage.id);
              const nextIndex = (currentIndex + 1) % collectionsData.length;
              setSelectedImage(collectionsData[nextIndex]);
            }}
            aria-label="Next image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          
          {/* Image */}
          <div 
            className="relative max-w-full max-h-full flex items-center justify-center"
            onClick={(e) => e.stopPropagation()}
          >
            <Image
              src={selectedImage.imageUrl}
              alt={selectedImage.alt}
              width={1200}
              height={1200}
              className={`max-w-full max-h-[85vh] object-contain ${selectedImage.imageUrl.includes('-back.JPG') ? 'rotate-[-90deg] scale-[0.85] origin-center' : ''}`}
              sizes="100vw"
            />
            
            {/* Caption */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/70 text-white">
              <h3 className="text-lg font-medium">{selectedImage.title}</h3>
              <p className="text-sm text-gray-300">{selectedImage.alt}</p>
            </div>
          </div>
        </div>
      )}
      
      <SiteFooter />
    </div>
  );
}